/**
 * 重构内容区域交互功能
 * 处理筛选、加载更多等功能
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeContentSections();
});

/**
 * 初始化所有内容板块
 */
function initializeContentSections() {
    // 初始化筛选功能
    initializeFilters();
    
    // 初始化加载更多功能
    initializeLoadMore();
    
    // 初始化卡片点击事件
    initializeCardClicks();
    
    // 初始化横向滚动
    initializeHorizontalScroll();
}

/**
 * 初始化筛选功能
 */
function initializeFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const section = this.closest('.content-section');
            const sectionId = section.id;
            const filterValue = this.dataset.filter;
            
            // 更新按钮状态
            section.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');
            
            // 执行筛选
            filterContent(sectionId, filterValue);
        });
    });
}

/**
 * 筛选内容
 */
function filterContent(sectionId, filterValue) {
    const section = document.getElementById(sectionId);
    const cards = section.querySelectorAll('.content-card');
    
    // 显示加载动画
    showFilterLoading(section);
    
    // 模拟API请求延迟
    setTimeout(() => {
        cards.forEach(card => {
            if (filterValue === 'all') {
                card.style.display = 'block';
            } else {
                // 根据不同板块的筛选逻辑
                const shouldShow = shouldShowCard(card, filterValue, sectionId);
                card.style.display = shouldShow ? 'block' : 'none';
            }
        });
        
        hideFilterLoading(section);
        
        // 检查是否有可见的卡片
        const visibleCards = section.querySelectorAll('.content-card[style*="block"]');
        toggleEmptyState(section, visibleCards.length === 0);
        
    }, 500);
}

/**
 * 判断卡片是否应该显示
 */
function shouldShowCard(card, filterValue, sectionId) {
    switch (sectionId) {
        case 'game-companion-section':
            return filterGameCard(card, filterValue);
        case 'city-companion-section':
            return filterCityCard(card, filterValue);
        case 'attraction-tickets-section':
            return filterTicketCard(card, filterValue);
        case 'group-up-section':
            return filterGroupCard(card, filterValue);
        default:
            return true;
    }
}

/**
 * 游戏玩伴筛选逻辑
 */
function filterGameCard(card, filterValue) {
    switch (filterValue) {
        case 'hot':
            return card.querySelector('.rank-tag.king') !== null;
        case 'online':
            return card.querySelector('.online-status.online') !== null;
        default:
            return true;
    }
}

/**
 * 城市玩伴筛选逻辑
 */
function filterCityCard(card, filterValue) {
    const category = card.querySelector('.activity-category span');
    if (!category) return true;
    
    const categoryText = category.textContent.toLowerCase();
    
    switch (filterValue) {
        case 'entertainment':
            return categoryText.includes('娱乐') || categoryText.includes('休闲');
        case 'sports':
            return categoryText.includes('运动') || categoryText.includes('健身');
        case 'food':
            return categoryText.includes('美食') || categoryText.includes('探店');
        default:
            return true;
    }
}

/**
 * 景点门票筛选逻辑
 */
function filterTicketCard(card, filterValue) {
    switch (filterValue) {
        case 'discount':
            return card.querySelector('.badge.discount') !== null;
        case 'popular':
            return card.querySelector('.badge.hot') !== null;
        case 'nearby':
            // 模拟距离筛选
            const distance = card.querySelector('.distance');
            if (distance) {
                const distanceValue = parseFloat(distance.textContent);
                return distanceValue < 15; // 15km以内
            }
            return true;
        default:
            return true;
    }
}

/**
 * 组局搭子筛选逻辑
 */
function filterGroupCard(card, filterValue) {
    const category = card.querySelector('.group-category span');
    if (!category) return true;
    
    const categoryText = category.textContent.toLowerCase();
    
    switch (filterValue) {
        case 'sports':
            return categoryText.includes('运动') || categoryText.includes('健身');
        case 'entertainment':
            return categoryText.includes('娱乐') || categoryText.includes('休闲');
        case 'study':
            return categoryText.includes('学习') || categoryText.includes('成长');
        default:
            return true;
    }
}

/**
 * 显示筛选加载状态
 */
function showFilterLoading(section) {
    const cardsContainer = section.querySelector('.cards-container');
    cardsContainer.style.opacity = '0.5';
    cardsContainer.style.pointerEvents = 'none';
}

/**
 * 隐藏筛选加载状态
 */
function hideFilterLoading(section) {
    const cardsContainer = section.querySelector('.cards-container');
    cardsContainer.style.opacity = '1';
    cardsContainer.style.pointerEvents = 'auto';
}

/**
 * 切换空状态显示
 */
function toggleEmptyState(section, show) {
    let emptyState = section.querySelector('.empty-state-message');
    
    if (show && !emptyState) {
        emptyState = document.createElement('div');
        emptyState.className = 'empty-state-message';
        emptyState.innerHTML = `
            <i class="fas fa-box-open"></i>
            <p>暂无符合条件的内容，试试其他筛选条件吧！</p>
        `;
        section.querySelector('.cards-container').after(emptyState);
    } else if (!show && emptyState) {
        emptyState.remove();
    }
}

/**
 * 初始化加载更多功能
 */
function initializeLoadMore() {
    const loadMoreButtons = document.querySelectorAll('.load-more-btn');
    
    loadMoreButtons.forEach(button => {
        button.addEventListener('click', function() {
            const section = this.closest('.content-section');
            loadMoreContent(section, this);
        });
    });
}

/**
 * 加载更多内容
 */
function loadMoreContent(section, button) {
    // 显示加载状态
    const originalText = button.textContent;
    button.textContent = '加载中...';
    button.disabled = true;
    
    // 模拟API请求
    setTimeout(() => {
        // 这里可以添加实际的内容加载逻辑
        showToast('更多内容正在开发中，敬请期待！');
        
        // 恢复按钮状态
        button.textContent = originalText;
        button.disabled = false;
    }, 1000);
}

/**
 * 初始化卡片点击事件
 */
function initializeCardClicks() {
    // 游戏玩伴卡片点击
    document.querySelectorAll('#game-companion-section .card-cta-button').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            if (this.disabled) return;
            
            const card = this.closest('.content-card');
            const title = card.querySelector('.card-title').textContent;
            showToast(`正在连接 ${title}...`);
        });
    });
    
    // 城市玩伴卡片点击
    document.querySelectorAll('#city-companion-section .card-cta-button').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            if (this.disabled) return;
            
            const card = this.closest('.content-card');
            const title = card.querySelector('.card-title').textContent;
            showToast(`正在报名 ${title}...`);
        });
    });
    
    // 景点门票卡片点击
    document.querySelectorAll('#attraction-tickets-section .card-cta-button').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const card = this.closest('.content-card');
            const title = card.querySelector('.card-title').textContent;
            showToast(`正在跳转到 ${title} 购买页面...`);
        });
    });
    
    // 组局搭子卡片点击
    document.querySelectorAll('#group-up-section .card-cta-button').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            if (this.disabled) return;
            
            const card = this.closest('.content-card');
            const title = card.querySelector('.card-title').textContent;
            showToast(`正在加入 ${title}...`);
        });
    });
}

/**
 * 初始化横向滚动
 */
function initializeHorizontalScroll() {
    const scrollContainers = document.querySelectorAll('.companions-scroll, .activities-scroll, .tickets-scroll, .groups-scroll');
    
    scrollContainers.forEach(container => {
        // 添加鼠标滚轮横向滚动支持
        container.addEventListener('wheel', function(e) {
            if (e.deltaY !== 0) {
                e.preventDefault();
                this.scrollLeft += e.deltaY;
            }
        });
        
        // 添加触摸滑动支持
        let isDown = false;
        let startX;
        let scrollLeft;
        
        container.addEventListener('mousedown', function(e) {
            isDown = true;
            startX = e.pageX - this.offsetLeft;
            scrollLeft = this.scrollLeft;
            this.style.cursor = 'grabbing';
        });
        
        container.addEventListener('mouseleave', function() {
            isDown = false;
            this.style.cursor = 'grab';
        });
        
        container.addEventListener('mouseup', function() {
            isDown = false;
            this.style.cursor = 'grab';
        });
        
        container.addEventListener('mousemove', function(e) {
            if (!isDown) return;
            e.preventDefault();
            const x = e.pageX - this.offsetLeft;
            const walk = (x - startX) * 2;
            this.scrollLeft = scrollLeft - walk;
        });
    });
}

/**
 * 显示Toast提示
 */
function showToast(message) {
    // 如果页面已有showToast函数，使用现有的
    if (typeof window.showToast === 'function') {
        window.showToast(message);
        return;
    }
    
    // 否则创建简单的toast
    const toast = document.createElement('div');
    toast.className = 'simple-toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-size: 14px;
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 2000);
}
