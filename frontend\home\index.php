<?php
// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();

// 检查用户是否已登录
$is_logged_in = isUserLoggedIn();

// 检查是否已同意用户协议
$agreed_to_terms = isset($_COOKIE['agreed_to_terms']) ? true : false;

// 如果是从表单提交同意，则设置cookie
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['agree_terms'])) {
    setcookie('agreed_to_terms', 'true', time() + (86400 * 30 * 12), "/"); // Cookie 有效期一年
    $agreed_to_terms = true;

    // 使用绝对路径重定向，避免循环
    $redirect_url = '/frontend/home/<USER>';
    header('Location: ' . $redirect_url);
    exit;
}

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// 获取用户信息 和 头像URL
$user_avatar_display_url = 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'; // 默认头像
$user_nameplate_url = null; // 铭牌URL
$user_nameplate_active = false; // 铭牌是否启用

// 只有在用户已登录的情况下才尝试获取用户信息
if ($is_logged_in) {
    try {
        $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
        $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $stmt = $pdo->prepare("SELECT avatar, nameplate_url, nameplate_active FROM users WHERE id = :user_id");
        $stmt->execute(['user_id' => $_SESSION['user_id']]);
        $user_data = $stmt->fetch(PDO::FETCH_ASSOC);

        // 如果用户存在且有头像，则使用用户的头像
        if ($user_data && !empty($user_data['avatar'])) {
            $user_avatar_display_url = $user_data['avatar']; // 使用用户数据库中的头像
        }
        // 如果用户存在且有铭牌，则使用用户的铭牌
        if ($user_data && !empty($user_data['nameplate_url']) && $user_data['nameplate_active'] == 1) {
            $user_nameplate_url = $user_data['nameplate_url'];
            $user_nameplate_active = true;
        }
        // 如果用户不存在或头像为空，则使用默认头像

    } catch (PDOException $e) {
        error_log("首页错误 (获取用户信息/头像): " . $e->getMessage());
        // 即使数据库错误，也尝试使用默认头像，避免页面完全卡死
        // $db_error = true; // 可以选择性设置
    }
}

// 引入高德地图API工具，并准备 JS API URL
require_once __DIR__ . '/../../sql/amap_location.php';
$amap_js_api_url = getAmapJsApiUrl();

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#F8F9FF">
    <?php if ($is_logged_in): ?>
    <meta name="user-id" content="<?php echo htmlspecialchars($_SESSION['user_id']); ?>">
    <?php endif; ?>
    <title>趣玩星球 - 探索有趣的生活</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="features/game_companion/css/game_companion_new.css">
    <link rel="stylesheet" href="css/refactored_content.css"> <!-- 重构内容区域样式 -->
    <link rel="stylesheet" href="css/new_refactored_styles.css"> <!-- 新重构样式 -->
    <?php
    include '../includes/bottom_nav.php';
    echo renderBottomNavCSS();
    ?>
    <!-- 保留Font Awesome用于其他图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 实时通知系统 -->
    <?php include '../includes/realtime_notifications.php'; ?>

    <style>
        /* CSS变量定义 - 使用新主题色 */
        :root {
            --primary-color: #6F7BF5;
            --primary-light: #A8B2F8;
            --primary-dark: #5A67E8;
            --secondary-color: #8B95F7;
            --accent-color: #FF6B9D;
            --gradient-primary: linear-gradient(135deg, #6F7BF5, #8B95F7);
            --gradient-secondary: linear-gradient(135deg, #A8B2F8, #6F7BF5);
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-light: #999999;
            --bg-white: #FFFFFF;
            --bg-light: #F8F9FA;
            --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
            --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.12);
            --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.16);
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;
        }

        /* 用户协议弹窗样式 - 使用主题色 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .terms-modal-container {
            width: 90%;
            max-width: 420px;
            position: relative;
            transform: scale(0.8) translateY(50px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal-overlay.active .terms-modal-container {
            transform: scale(1) translateY(0);
        }

        /* 弹窗主体 - 使用主题色渐变 */
        .terms-modal {
            position: relative;
            background: var(--gradient-primary);
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        /* 装饰性插画区域 */
        .terms-modal-illustration {
            position: relative;
            height: 160px;
            background: var(--gradient-primary);
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* CSS绘制的插画元素 */
        .illustration-elements {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 主要图形 - 盾牌图标 */
        .shield-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        }

        .shield-icon i {
            font-size: 32px;
            color: white;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        /* 装饰性圆圈 */
        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 3s ease-in-out infinite;
        }

        .decoration-circle:nth-child(1) {
            width: 20px;
            height: 20px;
            top: 20px;
            left: 30px;
            animation-delay: 0s;
        }

        .decoration-circle:nth-child(2) {
            width: 16px;
            height: 16px;
            top: 40px;
            right: 40px;
            animation-delay: 1s;
        }

        .decoration-circle:nth-child(3) {
            width: 12px;
            height: 12px;
            bottom: 30px;
            left: 50px;
            animation-delay: 2s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 关闭按钮 */
        .terms-modal-close {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10;
            transition: all 0.3s ease;
        }

        .terms-modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .terms-modal-close::before,
        .terms-modal-close::after {
            content: '';
            position: absolute;
            width: 14px;
            height: 2px;
            background: white;
            border-radius: 1px;
        }

        .terms-modal-close::before {
            transform: rotate(45deg);
        }

        .terms-modal-close::after {
            transform: rotate(-45deg);
        }

        /* 内容区域 */
        .terms-content-area {
            background: white;
            padding: 24px;
            border-radius: 0 0 24px 24px;
        }

        .terms-modal-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 16px;
            text-align: center;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .terms-scrollable-content {
            max-height: 280px;
            overflow-y: auto;
            padding-right: 8px;
            margin-bottom: 20px;
        }

        .terms-scrollable-content::-webkit-scrollbar {
            width: 4px;
        }

        .terms-scrollable-content::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .terms-scrollable-content::-webkit-scrollbar-thumb {
            background: var(--gradient-primary);
            border-radius: 2px;
        }

        .terms-scrollable-content p {
            margin-bottom: 12px;
            font-size: 0.875rem;
            line-height: 1.6;
            color: var(--text-secondary);
        }

        .terms-scrollable-content strong {
            color: var(--text-primary);
            font-weight: 600;
        }

        .terms-bullet-list {
            list-style: none;
            padding-left: 0;
            margin: 12px 0;
        }

        .terms-bullet-list li {
            position: relative;
            margin-bottom: 8px;
            font-size: 0.875rem;
            color: var(--text-secondary);
            padding-left: 20px;
            line-height: 1.6;
        }

        .terms-bullet-list li::before {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--secondary-color);
            font-size: 0.875rem;
        }

        .terms-scrollable-content a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .terms-scrollable-content a:hover {
            text-decoration: underline;
        }

        /* 设备信息说明区域 */
        .device-info-section {
            background: linear-gradient(135deg, var(--bg-light), #f0fffe);
            border-radius: var(--radius-md);
            padding: 16px;
            margin: 16px 0;
            border-left: 4px solid var(--primary-color);
        }

        .device-info-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .device-info-title i {
            font-size: 1rem;
            color: var(--primary-color);
        }

        .device-info-list {
            font-size: 0.8rem;
            color: var(--text-secondary);
            line-height: 1.5;
        }

        /* 按钮区域 */
        .terms-modal-actions {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .btn-agree {
            background: var(--gradient-primary);
            color: white;
            padding: 16px;
            border-radius: var(--radius-md);
            font-weight: 600;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(111, 123, 245, 0.3);
        }

        .btn-agree:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(111, 123, 245, 0.4);
        }

        .btn-agree:active {
            transform: translateY(0);
        }

        .disagree-text {
            font-size: 0.8rem;
            color: var(--text-light);
            text-align: center;
            line-height: 1.4;
        }

        /* 页面背景插画 - 星球主题 */
        .page-background-illustrations {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        /* 主星球 */
        .planet {
            position: absolute;
            top: 15%;
            right: 8%;
            width: 150px;
            height: 150px;
            background: radial-gradient(circle at 30% 30%, #A8B2F8, #6F7BF5, #5A67E8);
            border-radius: 50%;
            opacity: 0.08;
            animation: planetRotate 20s linear infinite;
            box-shadow:
                inset -20px -20px 40px rgba(90, 103, 232, 0.3),
                0 0 60px rgba(111, 123, 245, 0.2);
        }

        .planet::before {
            content: '';
            position: absolute;
            top: 20%;
            left: 25%;
            width: 30%;
            height: 30%;
            background: rgba(168, 178, 248, 0.4);
            border-radius: 50%;
            animation: planetRotate 15s linear infinite reverse;
        }

        .planet::after {
            content: '';
            position: absolute;
            bottom: 30%;
            right: 20%;
            width: 20%;
            height: 20%;
            background: rgba(139, 149, 247, 0.3);
            border-radius: 50%;
            animation: planetRotate 25s linear infinite;
        }

        /* 小星球 */
        .mini-planet {
            position: absolute;
            bottom: 25%;
            left: 12%;
            width: 80px;
            height: 80px;
            background: radial-gradient(circle at 40% 40%, #8B95F7, #6F7BF5);
            border-radius: 50%;
            opacity: 0.06;
            animation: miniPlanetFloat 15s ease-in-out infinite;
        }

        /* 金色星星 */
        .star {
            position: absolute;
            color: #FFD700;
            font-size: 16px;
            opacity: 0;
            animation: starTwinkle 3s ease-in-out infinite;
        }

        .star:nth-child(1) {
            top: 20%;
            right: 25%;
            animation-delay: 0s;
        }

        .star:nth-child(2) {
            top: 12%;
            right: 18%;
            font-size: 12px;
            animation-delay: 1s;
        }

        .star:nth-child(3) {
            top: 25%;
            right: 12%;
            font-size: 14px;
            animation-delay: 2s;
        }

        .star:nth-child(4) {
            bottom: 30%;
            left: 8%;
            font-size: 18px;
            animation-delay: 0.5s;
        }

        .star:nth-child(5) {
            bottom: 35%;
            left: 18%;
            font-size: 10px;
            animation-delay: 1.5s;
        }

        .star:nth-child(6) {
            top: 40%;
            left: 5%;
            font-size: 14px;
            animation-delay: 2.5s;
        }

        /* 不规则几何图形 */
        .geometric-shape {
            position: absolute;
            opacity: 0.04;
        }

        .shape-1 {
            top: 35%;
            left: 25%;
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            transform: rotate(45deg);
            border-radius: 0 50% 0 50%;
            animation: shapeFloat 12s ease-in-out infinite;
        }

        .shape-2 {
            bottom: 15%;
            right: 30%;
            width: 0;
            height: 0;
            border-left: 30px solid transparent;
            border-right: 30px solid transparent;
            border-bottom: 50px solid rgba(255, 107, 157, 0.3);
            animation: shapeFloat 18s ease-in-out infinite reverse;
        }

        .shape-3 {
            top: 60%;
            right: 8%;
            width: 40px;
            height: 40px;
            background: var(--gradient-secondary);
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            animation: shapeFloat 14s ease-in-out infinite;
            animation-delay: 3s;
        }

        /* 动画关键帧 */
        @keyframes planetRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes miniPlanetFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(180deg); }
        }

        @keyframes starTwinkle {
            0%, 100% { opacity: 0; transform: scale(0.8); }
            50% { opacity: 0.8; transform: scale(1.2); }
        }

        @keyframes shapeFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(120deg); }
            66% { transform: translateY(5px) rotate(240deg); }
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .terms-modal-container {
                width: 95%;
                max-width: none;
            }

            .terms-content-area {
                padding: 20px;
            }

            .terms-scrollable-content {
                max-height: 240px;
            }

            .terms-modal-illustration {
                height: 140px;
            }

            .shield-icon {
                width: 60px;
                height: 60px;
            }

            .shield-icon i {
                font-size: 24px;
            }

            /* 移动端星球效果调整 */
            .planet {
                width: 100px;
                height: 100px;
                opacity: 0.05;
            }

            .mini-planet {
                width: 60px;
                height: 60px;
                opacity: 0.04;
            }

            .star {
                font-size: 12px;
            }

            .geometric-shape {
                opacity: 0.02;
            }
        }

        /* 退出登录提示样式 */
        .user-avatar-square-link {
            position: relative;
        }

        .logout-tooltip {
            position: absolute;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .logout-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.8);
        }

        .logout-tooltip.show {
            opacity: 1;
            visibility: visible;
        }
    </style>
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 引入高德地图JS API -->
    <script type="text/javascript">
      window._AMapSecurityConfig = {
        securityJsCode: 'b2d738d21b1aed6f1ec0d61a9c935a7b' // 使用正确的安全密钥
      };
    </script>
    <script type="text/javascript" src="<?php echo htmlspecialchars($amap_js_api_url); ?>&callback=onAMapLoaded"></script>
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <?php if (!$agreed_to_terms): ?>
    <!-- 用户协议弹窗 - 全新设计 -->
    <div class="modal-overlay active" id="termsModalOverlay">
        <div class="terms-modal-container">
            <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="terms-modal">
                <!-- 插画装饰区域 -->
                <div class="terms-modal-illustration">
                    <button type="button" class="terms-modal-close" id="closeModal"></button>
                    <div class="illustration-elements">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                        <div class="shield-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div class="terms-content-area">
                    <div class="terms-modal-title">用户服务协议及隐私政策</div>

                    <div class="terms-scrollable-content">
                        <p><i class="fas fa-star" style="color: var(--accent-color);"></i> <strong>欢迎使用趣玩星球！</strong></p>
                        <p>为了更好地保护您的权益，请您仔细阅读并充分理解《用户服务协议》和《隐私政策》的全部内容。点击"同意并继续"，即表示您已阅读并同意以下条款。</p>

                        <p><strong><i class="fas fa-clipboard-list" style="color: var(--primary-color);"></i> 用户协议摘要：</strong></p>
                        <ul class="terms-bullet-list">
                            <li><strong>服务内容：</strong> 趣玩星球为您提供社交互动、活动组织、内容分享等服务，我们保留随时变更、中断或终止部分或全部服务的权利。</li>
                            <li><strong>账号规范：</strong> 您需对账号安全负责，不得将账号出借、转让或用于违法活动，否则我们有权限制或终止您的使用权限。</li>
                            <li><strong>用户行为：</strong> 您应遵守法律法规，不得发布违法、侵权、色情、暴力等不良信息，否则将承担相应法律责任。</li>
                            <li><strong>知识产权：</strong> 您在平台发布的内容，授权我们以合理方式使用；平台内容的知识产权归趣玩星球或第三方所有。</li>
                        </ul>

                        <p><strong><i class="fas fa-lock" style="color: var(--primary-color);"></i> 隐私政策摘要：</strong></p>
                        <ul class="terms-bullet-list">
                            <li><strong>信息收集：</strong> 我们会收集您的注册信息、位置信息、设备信息及使用记录，用于提供和优化服务。</li>
                            <li><strong>信息使用：</strong> 我们使用您的信息用于身份验证、服务提供、安全保障、体验优化及法律要求等目的。</li>
                            <li><strong>信息保护：</strong> 我们采取严格的数据安全措施保护您的个人信息，未经您同意不会向第三方分享您的个人敏感信息。</li>
                            <li><strong>权利说明：</strong> 您有权查询、更正、删除个人信息，管理隐私设置，注销账号等。如有疑问，可通过客服联系我们。</li>
                        </ul>

                        <!-- 设备信息收集说明 -->
                        <div class="device-info-section">
                            <div class="device-info-title">
                                <i class="fas fa-mobile-alt"></i>
                                设备信息收集说明
                            </div>
                            <div class="device-info-list">
                                <p><strong>我们可能收集的设备信息包括：</strong></p>
                                • <strong>设备标识：</strong>设备型号、操作系统版本、设备唯一标识符<br>
                                • <strong>网络信息：</strong>IP地址、网络类型、运营商信息<br>
                                • <strong>位置信息：</strong>GPS定位、基站定位、WiFi定位（需您授权）<br>
                                • <strong>使用数据：</strong>应用使用时长、功能使用频率、崩溃日志<br>
                                • <strong>浏览器信息：</strong>浏览器类型、版本、语言设置<br><br>
                                <strong>收集目的：</strong>用于提供个性化服务、保障账户安全、优化产品体验、进行数据分析和故障排查。我们承诺严格保护您的隐私，不会将您的个人信息用于其他商业目的。
                            </div>
                        </div>

                        <p><i class="fas fa-file-alt" style="color: var(--primary-color);"></i> 完整版协议请访问：<a href="../policies/user_agreement.php">《用户服务协议》</a>和<a href="../policies/privacy_policy.php">《隐私政策》</a></p>
                    </div>

                    <div class="terms-modal-actions">
                        <button type="submit" name="agree_terms" value="true" class="btn-agree">
                            <i class="fas fa-rocket"></i> 同意并继续探索
                        </button>
                        <p class="disagree-text">若您不同意本协议，将无法使用我们的核心服务功能</p>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <?php endif; ?>

    <!-- 页面背景插画 - 星球主题 -->
    <div class="page-background-illustrations">
        <!-- 主星球 -->
        <div class="planet"></div>

        <!-- 小星球 -->
        <div class="mini-planet"></div>

        <!-- 闪烁的金色星星 -->
        <div class="star">★</div>
        <div class="star">✦</div>
        <div class="star">✧</div>
        <div class="star">★</div>
        <div class="star">✦</div>
        <div class="star">✧</div>

        <!-- 不规则几何图形 -->
        <div class="geometric-shape shape-1"></div>
        <div class="geometric-shape shape-2"></div>
        <div class="geometric-shape shape-3"></div>
    </div>

    <!-- 新的页面头部，包含渐变背景 -->
    <div class="page-header-gradient-area">
        <div class="top-search-complex">
            <div class="main-search-bar">
                <div class="location-trigger" id="location">
                    <i class="fas fa-map-marker-alt location-icon"></i>
                    <span id="current-city-minimal">北海</span>
                    <i class="fas fa-chevron-down dropdown-icon"></i>
                </div>

                <input type="search" id="main-search-input" placeholder="搜搜北海的新鲜事">
                <button class="search-action-btn">
                    <span>搜索</span>
                </button>
            </div>
            <a href="../profile/index.php" class="user-avatar-square-link" id="userAvatarLink">
                <img src="<?php echo htmlspecialchars($user_avatar_display_url); ?>" alt="User Avatar" class="user-avatar-square-img">
                <?php if ($is_logged_in): ?>
                <div class="logout-tooltip" id="logoutTooltip">长按退出登录</div>
                <?php endif; ?>
            </a>
        </div>
        <div class="hot-search-tags-container">
            <span class="hot-tag"><i class="fas fa-fire"></i> 露营</span>
            <span class="hot-tag">BBQ</span>
            <span class="hot-tag">钓鱼</span>
            <span class="hot-tag">组局</span>
            <span class="hot-tag">旅游</span>
            <span class="hot-tag">电竞</span>
        </div>

        <!-- 金刚区移到这里 -->
        <div class="feature-nav-container">
            <div class="feature-nav">
                <div class="feature-item ripple" data-feature="game">
                    <div class="feature-icon">
                        <i class="far fa-gamepad"></i>
                    </div>
                    <span>游戏玩伴</span>
                </div>
                <div class="feature-item ripple active" data-feature="city">
                    <div class="feature-icon">
                        <i class="far fa-building"></i>
                    </div>
                    <span>城市玩伴</span>
                </div>
                <div class="feature-item ripple" data-feature="group">
                    <div class="feature-icon">
                        <i class="far fa-users"></i>
                    </div>
                    <span>组局约伴</span>
                </div>
                <div class="feature-item ripple" data-feature="ticket">
                    <div class="feature-icon">
                        <i class="far fa-ticket-alt"></i>
                    </div>
                    <span>景点门票</span>
                </div>
                <div class="active-tab-indicator-container">
                    <div class="active-tab-indicator"></div>
                </div>
            </div>
            <!-- 旧的子选项已移除，使用新的现代化胶囊式设计 -->
                <!-- “游戏玩伴”的子菜单已移除 -->
                <div id="sub-options-group" class="sub-options-list">
                    <div class="sub-option-tab active" data-sub-option="entertainment-group">娱乐</div>
                    <div class="sub-option-tab" data-sub-option="fishing">钓鱼</div>
                    <div class="sub-option-tab" data-sub-option="social-group">社交</div>
                    <div class="sub-option-tab" data-sub-option="sports-group">运动</div>
                    <div class="sub-option-tab" data-sub-option="outdoor">户外</div>
                </div>
                <div id="sub-options-ticket" class="sub-options-list">
                    <div class="sub-option-tab active" data-sub-option="amusement-park">游乐园</div>
                    <div class="sub-option-tab" data-sub-option="scenic-area">景区</div>
                    <div class="sub-option-tab" data-sub-option="park">公园</div>
                    <div class="sub-option-tab" data-sub-option="other-ticket">其他</div>
                </div>
                <!-- 新增：当前选择的城市显示 -->
                <div id="selected-city-display" class="selected-info-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span id="current-selected-city-text">北海</span>
                </div>
                <!-- 新增：当前选择的日期显示 -->
                <div id="selected-date-display" class="selected-info-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span id="current-selected-date-text">选择日期</span>
                </div>
                <!-- 新的游戏玩伴内容区域 -->
                <div id="game-companion-content-area" style="display: none;">
                    <!-- 新的游戏玩伴页面内容将在这里动态加载或静态放置 -->
                    <p>新的游戏玩伴页面正在设计中...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 全新重构的主内容区域 -->
    <div class="main-content-area" id="main-content-area">

        <!-- 内容将通过JavaScript动态加载 -->
    </div>

    <!-- 全新现代化的内容区域 -->
    <div class="modern-content-wrapper">
        <!-- 智能筛选栏 -->
        <div class="smart-filter-bar" id="smart-filter-bar">
            <div class="filter-container">
                <!-- 子选项胶囊 -->
                <div class="sub-options-pills" id="sub-options-pills">
                    <!-- 城市玩伴子选项 -->
                    <div class="pills-group active" id="pills-city">
                        <div class="pill-item active" data-sub-option="entertainment">
                            <i class="fas fa-gamepad"></i>
                            <span>娱乐</span>
                        </div>
                        <div class="pill-item" data-sub-option="travel">
                            <i class="fas fa-map-marked-alt"></i>
                            <span>旅游</span>
                        </div>
                        <div class="pill-item" data-sub-option="social">
                            <i class="fas fa-users"></i>
                            <span>社交</span>
                        </div>
                        <div class="pill-item" data-sub-option="sports">
                            <i class="fas fa-running"></i>
                            <span>运动</span>
                        </div>
                        <div class="pill-item" data-sub-option="food">
                            <i class="fas fa-utensils"></i>
                            <span>美食</span>
                        </div>
                        <div class="pill-item" data-sub-option="culture">
                            <i class="fas fa-theater-masks"></i>
                            <span>文化</span>
                        </div>
                    </div>

                    <!-- 游戏玩伴子选项 -->
                    <div class="pills-group" id="pills-game">
                        <div class="pill-item active" data-sub-option="moba">
                            <i class="fas fa-chess"></i>
                            <span>MOBA</span>
                        </div>
                        <div class="pill-item" data-sub-option="fps">
                            <i class="fas fa-crosshairs"></i>
                            <span>FPS</span>
                        </div>
                        <div class="pill-item" data-sub-option="mmorpg">
                            <i class="fas fa-dragon"></i>
                            <span>MMORPG</span>
                        </div>
                        <div class="pill-item" data-sub-option="mobile">
                            <i class="fas fa-mobile-alt"></i>
                            <span>手游</span>
                        </div>
                        <div class="pill-item" data-sub-option="casual">
                            <i class="fas fa-puzzle-piece"></i>
                            <span>休闲</span>
                        </div>
                        <div class="pill-item" data-sub-option="competitive">
                            <i class="fas fa-trophy"></i>
                            <span>竞技</span>
                        </div>
                    </div>

                    <!-- 组局搭子子选项 -->
                    <div class="pills-group" id="pills-group">
                        <div class="pill-item active" data-sub-option="entertainment-group">
                            <i class="fas fa-film"></i>
                            <span>娱乐</span>
                        </div>
                        <div class="pill-item" data-sub-option="fishing">
                            <i class="fas fa-fish"></i>
                            <span>钓鱼</span>
                        </div>
                        <div class="pill-item" data-sub-option="sports-group">
                            <i class="fas fa-basketball-ball"></i>
                            <span>运动</span>
                        </div>
                        <div class="pill-item" data-sub-option="outdoor">
                            <i class="fas fa-mountain"></i>
                            <span>户外</span>
                        </div>
                        <div class="pill-item" data-sub-option="study">
                            <i class="fas fa-book"></i>
                            <span>学习</span>
                        </div>
                        <div class="pill-item" data-sub-option="party">
                            <i class="fas fa-glass-cheers"></i>
                            <span>聚会</span>
                        </div>
                    </div>

                    <!-- 景点门票子选项 -->
                    <div class="pills-group" id="pills-ticket">
                        <div class="pill-item active" data-sub-option="amusement-park">
                            <i class="fas fa-ferris-wheel"></i>
                            <span>游乐园</span>
                        </div>
                        <div class="pill-item" data-sub-option="scenic-area">
                            <i class="fas fa-mountain"></i>
                            <span>景区</span>
                        </div>
                        <div class="pill-item" data-sub-option="museum">
                            <i class="fas fa-university"></i>
                            <span>博物馆</span>
                        </div>
                        <div class="pill-item" data-sub-option="park">
                            <i class="fas fa-tree"></i>
                            <span>公园</span>
                        </div>
                        <div class="pill-item" data-sub-option="aquarium">
                            <i class="fas fa-fish"></i>
                            <span>海洋馆</span>
                        </div>
                        <div class="pill-item" data-sub-option="zoo">
                            <i class="fas fa-paw"></i>
                            <span>动物园</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作工具栏 -->
        <div class="quick-toolbar">
            <div class="toolbar-left">
                <div class="location-selector" id="current-location-display">
                    <i class="fas fa-map-marker-alt"></i>
                    <span id="current-location-text">北海</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="date-selector" id="current-date-display">
                    <i class="fas fa-calendar-alt"></i>
                    <span id="current-date-text">今天</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
            <div class="toolbar-right">
                <button class="filter-toggle-btn" id="filter-toggle-btn">
                    <i class="fas fa-sliders-h"></i>
                </button>
                <button class="sort-btn" id="sort-btn">
                    <i class="fas fa-sort-amount-down"></i>
                </button>
            </div>
        </div>

        <!-- 动态内容展示区域 -->
        <div class="dynamic-content-display" id="dynamic-content-display">
            <!-- 内容将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 城市选择器弹窗 -->
    <div class="city-selector" id="city-selector">
        <div class="city-selector-header">
            <h3>选择城市</h3>
            <span class="close-btn" id="close-city-selector"><i class="fas fa-times"></i></span>
        </div>
        <!-- 新增：城市搜索框 -->
        <div class="city-search-container">
            <input type="search" id="city-search-input" class="city-search-bar" placeholder="搜索城市名称...">
        </div>
        <div class="auto-locate-section">
            <button id="auto-locate-btn" class="auto-locate-btn">
                <i class="fas fa-location-arrow"></i> 自动定位
            </button>
        </div>

        <!-- 新增：热门城市区域 -->
        <div class="hot-cities-section">
            <h4>热门城市</h4>
            <div class="city-list hot-city-list">
                <div class="city-item" data-city="北京市">北京市</div>
                <div class="city-item" data-city="上海市">上海市</div>
                <div class="city-item" data-city="广州市">广州市</div>
                <div class="city-item" data-city="深圳市">深圳市</div>
                <div class="city-item" data-city="杭州市">杭州市</div>
                <div class="city-item" data-city="成都市">成都市</div>
                <div class="city-item" data-city="重庆市">重庆市</div>
                <div class="city-item" data-city="武汉市">武汉市</div>
            </div>
        </div>

        <!-- 所有城市列表 -->
        <div class="all-cities-section">
            <h4>所有城市</h4>
            <div class="city-list all-city-list">
                <!-- 城市列表将由JS动态填充或保持现有静态列表 -->
                <!-- 以下为示例，实际列表可能更长或来自JS -->
                <div class="city-item" data-city="北京市">北京市</div>
                <div class="city-item" data-city="上海市">上海市</div>
                <div class="city-item" data-city="广州市">广州市</div>
                <div class="city-item" data-city="深圳市">深圳市</div>
                <div class="city-item" data-city="杭州市">杭州市</div>
                <div class="city-item" data-city="南京市">南京市</div>
                <div class="city-item" data-city="成都市">成都市</div>
                <div class="city-item" data-city="重庆市">重庆市</div>
                <div class="city-item" data-city="长沙市">长沙市</div>
                <div class="city-item" data-city="武汉市">武汉市</div>
                <div class="city-item" data-city="西安市">西安市</div>
                <div class="city-item" data-city="合肥市">合肥市</div>
                <div class="city-item" data-city="苏州市">苏州市</div>
                <div class="city-item" data-city="天津市">天津市</div>
                <div class="city-item" data-city="长春市">长春市</div>
                <div class="city-item" data-city="哈尔滨市">哈尔滨市</div>
                <div class="city-item" data-city="新疆">新疆</div>
                <div class="city-item" data-city="西藏">西藏</div>
                <div class="city-item" data-city="南宁市">南宁市</div>
                <div class="city-item" data-city="北海市">北海市</div>
                <div class="city-item" data-city="钦州市">钦州市</div>
                <div class="city-item" data-city="三亚市">三亚市</div>
                <div class="city-item" data-city="海口市">海口市</div>
                <div class="city-item" data-city="六盘水市">六盘水市</div>
                <div class="city-item" data-city="毕节市">毕节市</div>
                <div class="city-item" data-city="贵阳市">贵阳市</div>
                <div class="city-item" data-city="青岛市">青岛市</div>
                <div class="city-item" data-city="廊坊市">廊坊市</div>
                <div class="city-item" data-city="石家庄市">石家庄市</div>
                <div class="city-item" data-city="辽宁">辽宁</div>
                <div class="city-item" data-city="沈阳市">沈阳市</div>
                <div class="city-item" data-city="黑河市">黑河市</div>
                <div class="city-item" data-city="宁夏">宁夏</div>
                <!-- 更多城市... -->
            </div>
        </div>
    </div>

    <!-- 新增：日期选择弹窗 -->
    <div class="date-selector-modal" id="date-selector-modal">
        <div class="date-selector-header">
            <h3>选择日期</h3>
            <span class="close-btn" id="close-date-selector"><i class="fas fa-times"></i></span>
        </div>
        <div class="calendar-container">
            <div class="calendar-header">
                <button class="month-nav-btn prev-month" id="prev-month-btn"><i class="fas fa-chevron-left"></i></button>
                <div class="current-month-year" id="current-month-year-display"></div>
                <button class="month-nav-btn next-month" id="next-month-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
            <div class="calendar-weekdays">
                <span>日</span><span>一</span><span>二</span><span>三</span><span>四</span><span>五</span><span>六</span>
            </div>
            <div class="calendar-days" id="calendar-days-grid">
                <!-- JS会在这里填充日期 -->
            </div>
        </div>
         <div class="date-selector-footer">
            <button class="confirm-date-btn" id="confirm-date-btn">确定</button>
        </div>
    </div>

    <!-- 遮罩层 -->
    <div class="overlay" id="overlay"></div>

    <!-- 内容区域 (轮播图已移出) -->
    <div class="content-container">
        <!-- 活动分类、热门推荐、附近的人等内容将被移除 -->
        <!-- 如果有其他内容保留在 content-container, 它们会留在这里 -->
    </div>

    <!-- 新增：搜索页面覆盖层 -->
    <div class="search-overlay-page" id="search-overlay-page">
        <div class="search-overlay-header">
            <i class="fas fa-arrow-left search-overlay-back-btn" id="search-overlay-back-btn"></i>
            <div class="search-overlay-input-container">
                <i class="fas fa-search search-overlay-input-icon"></i>
                <input type="search" id="search-overlay-input" placeholder="搜索你感兴趣的内容">
            </div>
            <span class="search-overlay-cancel-btn" id="search-overlay-cancel-btn">取消</span>
        </div>

        <div class="search-overlay-content">
            <!-- 历史搜索 -->
            <div class="search-history-section" id="search-history-section" style="display: none;">
                <h4>
                    历史搜索
                    <span class="clear-history-btn" id="clear-history-btn">清空</span>
                </h4>
                <ul class="history-list" id="history-list">
                    <!-- 历史搜索项将通过JavaScript动态添加 -->
                </ul>
            </div>

            <div class="search-suggestions-section">
                <h4>热门搜索</h4>
                <div class="hot-keywords-list" id="hot-keywords-list">
                    <!-- 热门搜索将通过JavaScript动态加载 -->
                </div>
            </div>

            <div class="search-trending-section">
                <h4>热门榜单</h4>
                <ul class="trending-list" id="trending-list">
                    <!-- 热门榜单将通过JavaScript动态加载 -->
                </ul>
            </div>
        </div>
    </div>
    <!-- 搜索页面覆盖层结束 -->

    <!-- 搜索加载动画 -->
    <div class="search-loading-overlay" id="search-loading-overlay">
        <div class="four-star-loader">
            <div class="star-point"></div>
            <div class="star-point"></div>
            <div class="star-point"></div>
            <div class="star-point"></div>
        </div>
        <div class="search-loading-text">正在搜索中...</div>
    </div>

    <!-- 搜索结果页面 -->
    <div class="search-results-page" id="search-results-page">
        <div class="search-results-header">
            <i class="fas fa-arrow-left search-results-back-btn" id="search-results-back-btn"></i>
            <div class="search-results-keyword" id="search-results-keyword">搜索结果</div>
        </div>
        <div class="search-results-content" id="search-results-content">
            <!-- 搜索结果内容将通过JavaScript动态加载 -->
        </div>
    </div>

    <?php if (!$is_logged_in): ?>
    <!-- 登录提醒条 -->
    <div class="login-reminder">
        <span>登录后体验更多功能</span>
        <a href="../login/index.php" class="login-btn">立即登录</a>
    </div>
    <?php endif; ?>

    <?php echo renderBottomNav('home', $is_logged_in); ?>

    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <!-- 定位权限弹窗 -->
    <div class="modal-overlay" id="location-modal">
        <div class="modal-container">
            <div class="modal-title">位置权限请求</div>
            <div class="modal-content">
                为了提供更好的本地服务，趣玩星球需要获取您的位置信息。请允许位置权限以获取您所在城市。
            </div>
            <div class="modal-buttons">
                <button class="modal-button modal-button-secondary" id="deny-location">拒绝</button>
                <button class="modal-button modal-button-primary" id="allow-location">允许</button>
            </div>
        </div>
    </div>

    <!-- 不需要地图API -->
    <script src="js/script.js"></script>
    <script src="js/search.js"></script>
    <script src="js/refactored_content.js"></script> <!-- 重构内容区域交互 -->
    <script src="js/new_refactored_content.js"></script> <!-- 新重构内容系统 -->

    <!-- 条款弹窗脚本 -->
    <script>
        // 关闭弹窗按钮功能
        document.addEventListener('DOMContentLoaded', function() {
            const closeBtn = document.getElementById('closeModal');
            const modalOverlay = document.getElementById('termsModalOverlay');

            if (closeBtn && modalOverlay) {
                closeBtn.addEventListener('click', function() {
                    modalOverlay.classList.remove('active');
                });
            }
        });

        // Toast提示函数
        function showToast(message) {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.style.display = 'block';
            toast.style.animation = 'fadeIn 0.3s forwards';

            setTimeout(() => {
                toast.style.animation = 'fadeOut 0.3s forwards';
                setTimeout(() => {
                    toast.style.display = 'none';
                }, 300);
            }, 3000);
        }

        // 长按退出登录功能
        <?php if ($is_logged_in): ?>
        document.addEventListener('DOMContentLoaded', function() {
            const avatarLink = document.getElementById('userAvatarLink');
            const tooltip = document.getElementById('logoutTooltip');
            let longPressTimer;
            let isLongPress = false;

            if (avatarLink && tooltip) {
                // 鼠标/触摸开始
                function startLongPress(e) {
                    isLongPress = false;
                    tooltip.classList.add('show');

                    longPressTimer = setTimeout(() => {
                        isLongPress = true;
                        // 确认退出登录
                        if (confirm('确定要退出登录吗？')) {
                            window.location.href = '../logout.php';
                        }
                    }, 1000); // 1秒长按
                }

                // 鼠标/触摸结束
                function endLongPress(e) {
                    clearTimeout(longPressTimer);
                    tooltip.classList.remove('show');

                    // 如果不是长按，则正常跳转到个人页面
                    if (!isLongPress) {
                        setTimeout(() => {
                            if (!isLongPress) {
                                window.location.href = '../profile/index.php';
                            }
                        }, 100);
                    }
                    e.preventDefault();
                }

                // 鼠标事件
                avatarLink.addEventListener('mousedown', startLongPress);
                avatarLink.addEventListener('mouseup', endLongPress);
                avatarLink.addEventListener('mouseleave', () => {
                    clearTimeout(longPressTimer);
                    tooltip.classList.remove('show');
                });

                // 触摸事件
                avatarLink.addEventListener('touchstart', startLongPress);
                avatarLink.addEventListener('touchend', endLongPress);
                avatarLink.addEventListener('touchcancel', () => {
                    clearTimeout(longPressTimer);
                    tooltip.classList.remove('show');
                });

                // 阻止默认链接行为
                avatarLink.addEventListener('click', function(e) {
                    e.preventDefault();
                });
            }
        });
        <?php endif; ?>
    </script>
    <?php echo renderBottomNavJS($is_logged_in); ?>

    <?php if ($is_logged_in): ?>
    <!-- 实时通知组件 -->
    <script src="../components/realtime_notifications.js"></script>
    <script>
        // 设置当前用户ID供实时通知组件使用
        window.currentUserId = <?php echo json_encode($_SESSION['user_id']); ?>;
    </script>
    <?php endif; ?>
</body>
</html>
