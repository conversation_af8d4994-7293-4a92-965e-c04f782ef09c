<?php
// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();

// 检查用户是否已登录
if (!isUserLoggedIn()) {
    header('Location: ../login/index.php');
    exit;
}

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// 获取用户信息
try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = :user_id");
    $stmt->execute(['user_id' => $_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    // 如果用户不存在，注销并重定向到登录页
    if (!$user) {
        session_destroy();
        header('Location: ../login/index.php');
        exit;
    }
} catch (PDOException $e) {
    error_log("设置页面错误: " . $e->getMessage());
    $db_error = true;
}

// 处理退出登录请求
if (isset($_POST['logout'])) {
    // 清除会话
    session_destroy();

    // 清除记住我的Cookie
    if (isset($_COOKIE['remember_token'])) {
        setcookie('remember_token', '', time() - 3600, '/', '', false, true);
    }

    // 重定向到登录页
    header('Location: ../login/index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#1E90FF">
    <title>设置 - 趣玩星球</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 设置页面特定样式 */
        .header-bar {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: #1E90FF;
            color: white;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .back-button {
            margin-right: 15px;
            font-size: 18px;
        }

        .header-title {
            font-size: 18px;
            font-weight: bold;
        }

        .settings-section {
            margin: 15px;
        }

        .settings-group {
            background-color: white;
            border-radius: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .settings-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .settings-item:last-child {
            border-bottom: none;
        }

        .settings-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 18px;
            color: #1E90FF;
        }

        .danger-icon {
            color: #FF6B6B;
        }

        .settings-label {
            flex: 1;
            font-size: 15px;
            color: #333;
        }

        .danger-label {
            color: #FF6B6B;
        }

        .settings-arrow {
            color: #ccc;
            font-size: 14px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #1E90FF;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .logout-button {
            display: block;
            width: 100%;
            padding: 15px;
            background-color: white;
            color: #FF6B6B;
            border: none;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            text-align: center;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            margin-top: 30px;
        }

        .logout-button:active {
            background-color: #f9f9f9;
        }

        .version-info {
            text-align: center;
            margin-top: 30px;
            color: #999;
            font-size: 13px;
        }
    </style>
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">设置</div>
    </div>

    <!-- 设置选项 -->
    <div class="settings-section">
        <div class="settings-group">
            <a href="settings/profile_edit.php" class="settings-item">
                <div class="settings-icon"><i class="fas fa-user-edit"></i></div>
                <div class="settings-label">编辑资料</div>
                <div class="settings-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="settings/verification.php" class="settings-item">
                <div class="settings-icon"><i class="fas fa-id-card"></i></div>
                <div class="settings-label">实名认证</div>
                <div class="settings-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="settings/account_security.php" class="settings-item">
                <div class="settings-icon"><i class="fas fa-shield-alt"></i></div>
                <div class="settings-label">账号安全</div>
                <div class="settings-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
        </div>

        <div class="settings-group">
            <a href="settings/notification.php" class="settings-item">
                <div class="settings-icon"><i class="fas fa-bell"></i></div>
                <div class="settings-label">通知设置</div>
                <div class="settings-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <div class="settings-item">
                <div class="settings-icon"><i class="fas fa-moon"></i></div>
                <div class="settings-label">深色模式</div>
                <label class="toggle-switch">
                    <input type="checkbox" id="dark-mode-toggle">
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <a href="settings/privacy.php" class="settings-item">
                <div class="settings-icon"><i class="fas fa-lock"></i></div>
                <div class="settings-label">隐私设置</div>
                <div class="settings-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
        </div>

        <div class="settings-group">
            <a href="../rules/index.php" class="settings-item">
                <div class="settings-icon"><i class="fas fa-book"></i></div>
                <div class="settings-label">趣玩规则中心</div>
                <div class="settings-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="../policies/user_agreement.php" class="settings-item">
                <div class="settings-icon"><i class="fas fa-file-contract"></i></div>
                <div class="settings-label">用户协议</div>
                <div class="settings-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="../policies/privacy_policy.php" class="settings-item">
                <div class="settings-icon"><i class="fas fa-user-shield"></i></div>
                <div class="settings-label">隐私政策</div>
                <div class="settings-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="settings/about.php" class="settings-item">
                <div class="settings-icon"><i class="fas fa-info-circle"></i></div>
                <div class="settings-label">关于趣玩星球</div>
                <div class="settings-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
        </div>

        <div class="settings-group">
            <a href="settings/feedback.php" class="settings-item">
                <div class="settings-icon"><i class="fas fa-comment-dots"></i></div>
                <div class="settings-label">意见反馈</div>
                <div class="settings-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="settings/clear_cache.php" class="settings-item">
                <div class="settings-icon"><i class="fas fa-broom"></i></div>
                <div class="settings-label">清除缓存</div>
                <div class="settings-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="#" class="settings-item" id="delete-account">
                <div class="settings-icon danger-icon"><i class="fas fa-user-slash"></i></div>
                <div class="settings-label danger-label">注销账号</div>
                <div class="settings-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
        </div>

        <form method="post" id="logout-form">
            <button type="submit" name="logout" class="logout-button">退出登录</button>
        </form>

        <div class="version-info">
            趣玩星球 v1.0.0
        </div>
    </div>

    <script src="js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加设置项点击效果
            const settingsItems = document.querySelectorAll('.settings-item');
            settingsItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 点击效果
                    this.style.backgroundColor = '#f5f5f5';
                    setTimeout(() => {
                        this.style.backgroundColor = 'white';
                    }, 150);
                });
            });

            // 深色模式切换
            const darkModeToggle = document.getElementById('dark-mode-toggle');
            darkModeToggle.addEventListener('change', function() {
                if (this.checked) {
                    showToast('深色模式即将上线，敬请期待');
                    setTimeout(() => {
                        this.checked = false;
                    }, 500);
                }
            });

            // 注销账号确认
            const deleteAccount = document.getElementById('delete-account');
            deleteAccount.addEventListener('click', function(e) {
                e.preventDefault();
                if (confirm('确定要注销账号吗？此操作不可逆，您的所有数据将被删除。')) {
                    showToast('账号注销功能即将上线，敬请期待');
                }
            });
        });
    </script>
</body>
</html>
