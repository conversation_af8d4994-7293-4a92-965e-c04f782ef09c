/* CSS变量定义 - 主题色系统 */
:root {
    --primary-color: #6F7BF5;
    --primary-light: #A8B2F8;
    --primary-dark: #5A67E8;
    --secondary-color: #8B95F7;
    --accent-color: #FF6B9D;
    --gradient-primary: linear-gradient(135deg, #6F7BF5, #8B95F7);
    --gradient-secondary: linear-gradient(135deg, #A8B2F8, #6F7BF5);
    --gradient-accent: linear-gradient(135deg, #FF6B9D, #FFB6C1);
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #999999;
    --bg-white: #FFFFFF;
    --bg-light: #F8F9FA;
    --bg-gradient: #FFFFFF;
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.16);
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.4s ease;
}

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

body {
    background: var(--bg-white);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    position: relative;
    padding-bottom: 60px;
    overflow-x: hidden;
}

a {
    text-decoration: none;
    color: inherit;
}

/* Toast提示样式 - 年轻化设计 */
.toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--gradient-primary);
    color: white;
    padding: 16px 24px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 9999;
    display: none;
    font-size: 16px;
    font-weight: 600;
    max-width: 85%;
    text-align: center;
    pointer-events: none;
    line-height: 1.5;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* --- 页面头部区域 - 浅色渐变主题设计 --- */
.page-header-gradient-area {
    padding: 12px 15px 15px 15px;
    background: linear-gradient(to bottom,
        rgba(111, 123, 245, 0.15) 0%,
        rgba(111, 123, 245, 0.12) 20%,
        rgba(111, 123, 245, 0.08) 40%,
        rgba(111, 123, 245, 0.04) 70%,
        rgba(255, 255, 255, 1) 100%
    );
    z-index: 1;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.page-header-gradient-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(64,224,208,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(6,214,160,0.1)"/><circle cx="60" cy="80" r="1" fill="rgba(255,107,157,0.1)"/></svg>');
    pointer-events: none;
    z-index: -1;
}

.top-search-complex {
    display: flex;
    align-items: center;
    gap: 8px; /* Reduced gap */
    margin-bottom: 10px;
}

.main-search-bar {
    flex-grow: 1;
    display: flex;
    align-items: center;
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 6px 6px 6px 12px;
    height: 42px;
    box-shadow: var(--shadow-md);
    position: relative;
    border: 2px solid rgba(111, 123, 245, 0.1);
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
}

.main-search-bar:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(111, 123, 245, 0.1);
    transform: translateY(-1px);
}

.location-trigger {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 6px 10px;
    margin-right: 8px;
    border-right: 2px solid rgba(111, 123, 245, 0.2);
    font-size: 14px;
    color: var(--text-primary);
    white-space: nowrap;
    height: 100%;
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
    flex-shrink: 0;
    min-width: 0;
}

.location-trigger:hover {
    background: rgba(111, 123, 245, 0.05);
    color: var(--primary-color);
}

.location-trigger .location-icon {
    font-size: 12px;
    margin-right: 8px;
    color: var(--primary-color);
    transition: var(--transition-fast);
}

.location-trigger #current-city-minimal {
    font-weight: 600;
}

.location-trigger .dropdown-icon {
    font-size: 12px;
    margin-left: 6px;
    color: var(--primary-color);
    transition: var(--transition-fast);
}

.location-trigger:hover .dropdown-icon {
    transform: rotate(180deg);
}

.search-icon-inline {
    font-size: 14px; /* Slightly smaller */
    color: #888;
    margin-right: 6px;
}

#main-search-input {
    flex-grow: 1;
    border: none;
    background: transparent;
    outline: none;
    font-size: 14px;
    color: #333;
    height: 100%; /* Fill search bar height */
    padding-right: 5px; /* Space before internal search button */
}

#main-search-input::placeholder {
    color: #b0b0b0;
    font-weight: 400;
}

.user-avatar-square-link {
    display: block;
    width: 36px;
    height: 36px;
    flex-shrink: 0;
}

.user-avatar-square-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid #e0e0e0; /* Lighter border */
}

.search-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    height: 30px;
    background: linear-gradient(135deg, #6F7BF5, #667EEA);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    flex-shrink: 0;
    min-width: 56px;
    box-shadow: 0 2px 8px rgba(111, 123, 245, 0.25);
}

.search-action-btn:hover {
    background: linear-gradient(135deg, #5A67E8, #5B73E8);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(111, 123, 245, 0.35);
}

.search-action-btn span { /* Ensure span inside button is styled if needed */
    display: block;
}

.search-action-btn:active {
    opacity: 0.8;
}

.hot-search-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding-top: 2px; /* Small space from search complex */
}

.hot-tag {
    background: rgba(255, 255, 255, 0.8);
    color: var(--text-secondary);
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    border: 2px solid rgba(111, 123, 245, 0.2);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    flex-shrink: 0;
}

.hot-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
    z-index: -1;
}

.hot-tag:hover::before {
    left: 0;
}

.hot-tag:hover {
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.hot-tag i {
    margin-right: 6px;
    color: var(--accent-color);
    transition: var(--transition-fast);
}

.hot-tag:hover i {
    color: white;
    transform: scale(1.1);
}

/* --- 移除或注释旧的顶部和搜索相关样式 --- */
/* .top-header-container (旧的整体顶部栏) */
/* ... (原有的 .top-header-container 及其子元素 .location, .site-title, .user-avatar-link, .user-avatar 样式可以注释或删除) ... */
.top-header-container {
    /* display: none !important; */ /* 如果HTML还没移除，可以强制隐藏 */
}

/* .carousel (轮播图) */
.carousel {
    /* display: none !important; */ /* 隐藏轮播图 */
}
/* .carousel-inner, .carousel-item, .carousel-indicators 等相关样式也可以注释或删除 */

/* .search-bar-container (旧的独立搜索栏容器) */
.search-bar-container {
    /* display: none !important; */
}
/* .search-bar-container .search-bar 及其子元素样式也可以注释或删除 */


/* --- 调整页面主要内容容器的上边距 --- */
.content-container {
    padding-top: 0;
    background-color: #f8f9fa;
    position: relative; /* To ensure its content flows correctly below the absolute/relative header parts */
    z-index: 2; /* Above page header gradient if they were to overlap text, but generally not needed if layout is clear */
}

/* 通用的 .location 和 .search-bar 样式，如果其他地方不再使用，可以考虑移除 */
/* 或者保留，以防万一 */
.location { /* 通用样式 */
    /* ... (检查是否与其他地方的.location冲突或是否仍被需要) ... */
}

.search-bar { /* 通用样式 */
    /* ... (检查是否与其他地方的.search-bar冲突或是否仍被需要) ... */
}


/* 其他现有样式 (如城市选择器、底部导航等) 保持不变 */
/* .city-selector { ... } */
/* .overlay { ... } */
/* .feature-nav { ... } */
/* ... etc ... */


/* 城市选择器样式 */
.city-selector {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    /* background-color: white; */ /* Ensure this is commented or removed */
    background: linear-gradient(to bottom, #FFF0F5 0%, white 30%) !important; /* Adjusted gradient for more prominent top color */
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    padding: 15px; /* 统一padding */
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    max-height: 85vh; /* 稍微增加最大高度 */
    display: flex; /* 改为flex布局，方便管理子元素高度 */
    flex-direction: column;
}

.city-selector.active {
    transform: translateY(0);
}

.city-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px; /* 减小下方间距 */
    flex-shrink: 0; /* 防止头部被压缩 */
}

/* 新增：城市搜索框样式 */
.city-search-container {
    margin-bottom: 10px;
    flex-shrink: 0; /* 防止搜索框被压缩 */
}

.city-search-bar {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    box-sizing: border-box;
}

.city-search-bar:focus {
    outline: none;
    border-color: #6F7BF5;
    box-shadow: 0 0 0 2px rgba(111, 123, 245, 0.2);
}

.auto-locate-section {
    margin-bottom: 10px;
    flex-shrink: 0;
    text-align: center; /* Center the button */
}

/* Styling for the auto-locate button */
#auto-locate-btn {
    background: linear-gradient(to right, #ADD8E6, #E6E6FA) !important; /* Gradient with !important */
    color: #333333 !important; /* Darker text for contrast */
    border: none !important;
    padding: 10px 15px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    cursor: pointer !important;
    transition: opacity 0.2s ease, box-shadow 0.2s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    font-weight: 500;
}

#auto-locate-btn i {
    margin-right: 8px !important;
    font-size: 1em; /* Ensure icon size is relative to button font size */
}

#auto-locate-btn:hover {
    opacity: 0.9 !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

#auto-locate-btn:active {
    opacity: 0.8 !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.hot-cities { /* 这个类名现在用于包含所有城市列表的容器 */
    flex-grow: 1; /* 使城市列表占据剩余空间 */
    overflow-y: auto; /* 城市列表内部滚动 */
}

.hot-cities h4 {
    font-size: 15px;
    color: #666;
    margin-bottom: 10px; /* 减小下方间距 */
    padding-top: 5px; /* 与上方元素的间距 */
}

.city-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px; /* 减小城市项之间的间距 */
    padding-bottom: 10px; /* 底部留白，防止滚动条遮挡 */
}

.city-item {
    padding: 7px 12px; /* 调整内边距 */
    background-color: #f0f0f0;
    border-radius: 18px; /* 调整圆角 */
    font-size: 13px; /* 调整字体大小 */
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
}

.city-item:active {
    background-color: #1E90FF;
    color: white;
}

/* 遮罩层 */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

/* 轮播图样式 */
/* 移除轮播图的额外样式，因为它现在不是第一个元素了 */
/* .carousel {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    margin-bottom: 25px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    height: 200px;
} */

.carousel-inner {
    width: 100%;
    height: 100%;
    position: relative;
    /* display: flex; */ /* 移除 display: flex，因为JS控制的是opacity淡入淡出 */
    /* transition: transform 0.5s ease; */ /* 移除 transform 过渡，与当前JS逻辑不符 */
}

.carousel-item {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.carousel-item.active {
    opacity: 1;
    z-index: 1;
}

.carousel-item img {
    width: 100%;
    height: 100%;
    object-fit: contain; /* 修改为 contain */
}

/* 对于新的轮播图，确保 caption 在需要时仍然可用，但目前HTML中注释掉了 */
.carousel-item .carousel-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
    color: white;
    padding: 15px;
    z-index: 2;
}
.carousel-item .carousel-caption h3 {
    font-size: 18px; /* 调整大小 */
    margin-bottom: 4px;
    font-weight: 600;
}
.carousel-item .carousel-caption p {
    font-size: 13px; /* 调整大小 */
    opacity: 0.9;
}

/* 功能导航样式 - 年轻化设计 */
.feature-nav-container {
    background: transparent;
    padding-bottom: 0;
    position: relative;
    z-index: 3;
}

.feature-nav {
    display: flex;
    justify-content: space-around;
    padding: 20px 16px 16px 16px;
    background: transparent;
    position: relative;
    gap: 12px;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    text-align: center;
    cursor: pointer;
    padding: 4px 8px;
    position: relative;
    background: transparent;
    transition: var(--transition-normal);
    max-width: 80px;
}

.feature-item:hover {
    transform: translateY(-1px);
}

.feature-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    font-size: 20px;
    color: white;
    background: var(--primary-color);
    box-shadow: 0 2px 8px rgba(111, 123, 245, 0.2);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

/* 不同功能图标的个性化颜色 - 使用统一主题色 */
.feature-item:nth-child(1) .feature-icon {
    background: #6F7BF5;
}

.feature-item:nth-child(2) .feature-icon {
    background: #6F7BF5;
}

.feature-item:nth-child(3) .feature-icon {
    background: #6F7BF5;
}

.feature-item:nth-child(4) .feature-icon {
    background: #6F7BF5;
}

.feature-item:hover .feature-icon {
    background: #5A67E8;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(111, 123, 245, 0.3);
}

.feature-icon i {
    position: relative;
    z-index: 1;
    transition: var(--transition-fast);
}

.feature-item:hover .feature-icon i {
    transform: scale(1.1);
}

.feature-item:active .feature-icon {
    transform: scale(0.95);
}

.feature-item span {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
    transition: var(--transition-fast);
    line-height: 1.2;
    margin-top: 2px;
}

.feature-item:hover span {
    color: var(--primary-color);
}

.feature-item.active span {
    color: var(--primary-color);
    font-weight: 700;
}

.feature-item.active .feature-icon {
    background: #5A67E8;
    box-shadow: 0 4px 16px rgba(111, 123, 245, 0.4);
    transform: scale(1.02);
}

.feature-item.active .feature-icon i {
    transform: scale(1.05);
}

/* Sub-options-wrapper is the large rounded container for sub-menu items */
.sub-options-wrapper {
    position: relative; /* Ensures z-index context and proper positioning for children */
    background-color: #FFFFFF; /* White background */
    border-radius: 10px 10px 0 0; /* 只有顶部圆角 */
    margin: 0 -15px -15px -15px; /* 负边距抵消父容器的padding，实现真正全屏，包括底部 */
    padding: 15px; /* Inner padding for content within the white box */
    padding-bottom: 80px; /* 底部额外padding，为底部导航栏留空间 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08); /* Subtle shadow */
    min-height: calc(100vh - 280px); /* 确保有足够高度 */
}

/* 新增：子菜单下方信息项（城市、日期）的通用样式 */
.selected-info-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
    font-size: 15px; /* 字体稍大 */
    font-weight: bold; /* 加粗 */
    color: #333;
    cursor: pointer;
    border-top: 1px solid #f0f0f0; /* 分隔线上方元素 */
    margin-top: 15px; /* 与上方子菜单的间距 */
}

.selected-info-item i {
    margin-right: 8px;
    color: #555;
}

/* 新增：动态搜索按钮样式 */
.dynamic-search-button {
    display: block;
    width: 100%;
    padding: 12px 15px;
    margin-top: 15px; /* 与上方日期选择的间距 */
    background: linear-gradient(135deg, #6F7BF5, #FF6B9D); /* 魅力紫到粉色渐变 */
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.dynamic-search-button:hover {
    opacity: 0.9;
    box-shadow: 0 4px 10px rgba(111, 123, 245, 0.3);
}

.dynamic-search-button:active {
    transform: scale(0.98);
    opacity: 0.8;
}

/* 新增：游戏玩伴专属筛选条件样式 */
#game-specific-filters {
    display: flex; /* 改为 flex 布局以便更好地控制子元素 */
    flex-direction: column; /* 垂直排列筛选条件 */
    gap: 12px; /* 筛选条件之间的间距 */
    padding: 10px 0; /* 上下内边距 */
    margin-top: 10px; /* 与上方元素的间距 */
    border-top: 1px solid #f0f0f0; /* 顶部添加分隔线 */
}

.game-filter-item {
    display: flex;
    align-items: center;
    gap: 8px; /* 标签和选择框之间的间距 */
}

.game-filter-item label {
    font-size: 14px;
    color: #555;
    flex-shrink: 0; /* 防止标签被压缩 */
    width: 60px; /* 调整标签宽度，确保对齐 */
    text-align: right; /* 文本右对齐 */
}

/* Custom select styles */
.game-filter-item select {
    flex-grow: 1;
    padding: 10px 12px;
    font-size: 14px;
    color: #333;
    background-color: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 8px;
    appearance: none; /* Remove default arrow */
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23333'%3E%3Cpath fill-rule='evenodd' d='M8 11.293l-4.146-4.147a.5.5 0 0 1 .708-.708L8 9.879l3.438-3.438a.5.5 0 0 1 .707.708L8 11.293z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px 16px;
    outline: none;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
}

.game-filter-item select:focus {
    border-color: #6F7BF5;
    box-shadow: 0 0 0 3px rgba(111, 123, 245, 0.25);
}

.game-filter-item select option {
    padding: 10px;
    background-color: #fff;
    color: #333;
}

/* 确保第一个 selected-info-item 没有上边框和上边距，如果它直接跟在 sub-options-list 后面 */
.sub-options-list + .selected-info-item {
    margin-top: 15px;
    padding-top: 10px; /* 调整这个padding来控制与灰色条的距离 */
    /* border-top: 1px solid #f0f0f0; /* 可选，如果希望城市和日期之间有分隔 */
}

.active-tab-indicator-container {
    position: absolute;
    bottom: 0px; /* Indicator container bottom edge aligns with feature-nav bottom padding edge */
    left: 0; /* JS controls this for horizontal alignment */
    width: 26px; /* Width of the base of the semi-circle */
    height: 13px; /* Height of the semi-circle */
    display: flex;
    justify-content: center;
    align-items: flex-end; /* Align the indicator to the bottom of this container */
    transition: left 0.3s ease-out;
    pointer-events: none;
    z-index: 5;
    /* overflow: hidden; */ /* Not needed if indicator is sized correctly */
}

.active-tab-indicator {
    width: 100%; /* Takes full width of container (26px) */
    height: 100%; /* Takes full height of container (13px) */
    background-color: white; /* Same color as sub-options-wrapper */
    border-radius: 13px 13px 0 0; /* Top-left and top-right radius make it a semi-circle pointing up */
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.08); /* Subtle shadow on the top curved edge for depth */
}

.sub-options-list {
    display: none; /* Hidden by default */
    flex-wrap: wrap;
    gap: 10px;
    padding-top: 10px; /* Space below indicator */
}

.sub-options-list.active {
    display: flex;
    gap: 6px; /* Changed: Reduced gap between tabs */
    padding: 6px 8px;
    background-color: #e9ecef;
    border-radius: 8px;
    margin-top: 0;
    box-shadow: none;
    overflow-x: auto; /* 允许水平滚动 */
    overflow-y: hidden; /* 禁止垂直滚动 */
    flex-wrap: nowrap; /* 禁止换行，强制水平排列 */
    position: sticky; /* 固定位置 */
    top: 0; /* 固定在顶部 */
    z-index: 10; /* 确保在其他内容之上 */
    scrollbar-width: none; /* Firefox 隐藏滚动条 */
    -ms-overflow-style: none; /* IE/Edge 隐藏滚动条 */
    touch-action: pan-x; /* 只允许水平滑动 */
}

/* 隐藏 Webkit 浏览器的滚动条 */
.sub-options-list.active::-webkit-scrollbar {
    display: none;
}

.sub-options-list span { /* This was the old style for direct span children */
    /* display: block; */ /* Remove this if changing to tabs */
    /* padding: 8px 15px; */
    /* border-radius: 20px; */
    /* background-color: #e9ecef; */
    /* color: #495057; */
    /* cursor: pointer; */
    /* transition: background-color 0.2s, color 0.2s; */
    /* text-align: center; */
    /* font-size: 13px; */
}

/* New styles for .sub-option-tab */
.sub-option-tab {
    padding: 7px 10px; /* Changed: Reduced horizontal padding */
    border: none;
    border-radius: 0;
    font-size: 13px;
    color: #555;
    background-color: transparent;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;
}

.sub-option-tab:hover {
    color: #333; /* Slightly darker text on hover */
    /* background-color: transparent; or a very subtle hover on the grey bar if desired */
}

.sub-option-tab.active {
    background-color: #ffffff; /* Active background: white (the "small white box") */
    color: #6F7BF5;
    border-radius: 6px; /* Rounded corners for the active white box */
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1); /* Slightly more pronounced shadow for active tab */
    padding: 7px 14px; /* Ensure padding is consistent */
}

/* 波纹效果 */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::after {
    content: "";
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
    background-repeat: no-repeat;
    background-position: 50%;
    transform: scale(10, 10);
    opacity: 0;
    transition: transform 0.5s, opacity 0.5s;
}

.ripple:active::after {
    transform: scale(0, 0);
    opacity: 0.3;
    transition: 0s;
}

/* 活动分类 */
.category-section {
    margin-bottom: 25px;
}

.category-tabs {
    display: flex;
    overflow-x: auto;
    padding-bottom: 10px;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.category-tabs::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.category-tab {
    flex-shrink: 0;
    padding: 8px 20px;
    margin-right: 10px;
    background-color: #f0f0f0;
    border-radius: 20px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-tab.active {
    background-color: #1E90FF;
    color: white;
    box-shadow: 0 4px 8px rgba(30, 144, 255, 0.3);
}

/* 推荐内容样式 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-title {
    display: flex;
    flex-direction: column;
}

.section-title h2 {
    font-size: 20px;
    color: #333;
    font-weight: 600;
    margin-bottom: 4px;
}

.section-subtitle {
    font-size: 13px;
    color: #999;
}

.more-link {
    font-size: 14px;
    color: #1E90FF;
    display: flex;
    align-items: center;
}

.more-link i {
    margin-left: 3px;
    font-size: 12px;
}

.recommendation-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

.recommendation-item {
    background-color: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.recommendation-item:active {
    transform: scale(0.98);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
}

.recommendation-image {
    position: relative;
    height: 180px;
}

.recommendation-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.recommendation-tag {
    position: absolute;
    top: 12px;
    left: 12px;
    background-color: rgba(30, 144, 255, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.recommendation-bookmark {
    position: absolute;
    top: 12px;
    right: 12px;
    background-color: rgba(255, 255, 255, 0.9);
    color: #999;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.recommendation-bookmark:active {
    transform: scale(0.9);
    color: #FF6B6B;
}

.recommendation-info {
    padding: 16px;
}

.recommendation-info h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
}

.location {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.location i {
    margin-right: 5px;
    color: #1E90FF;
}

.current-city {
    color: #1E90FF;
    font-weight: 500;
}

.recommendation-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price {
    font-size: 18px;
    color: #FF6B6B;
    font-weight: 600;
}

.rating {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #FFA41B;
    font-weight: 500;
}

.rating i {
    margin-right: 3px;
}

.review-count {
    color: #999;
    font-size: 12px;
    font-weight: normal;
    margin-left: 3px;
}

/* 附近的人样式 */
.nearby-users {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

.user-card {
    background-color: white;
    border-radius: 16px;
    padding: 15px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.user-card:active {
    transform: scale(0.98);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    position: relative;
    border: 2px solid #f0f0f0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-status {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ccc;
    border: 2px solid white;
}

.user-status.online {
    background-color: #06D6A0;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.user-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.user-tag {
    background-color: #f0f0f0;
    color: #666;
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 10px;
}

.user-distance {
    font-size: 14px;
    color: #1E90FF;
    font-weight: 500;
}

/* 波纹效果样式 */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.7);
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    animation: ripple 0.6s linear;
    transform: scale(0);
    pointer-events: none;
    z-index: 10;
}

@keyframes ripple {
    to {
        transform: scale(2.5);
        opacity: 0;
    }
}

/* Toast动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    to {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
}

/* 居中弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background-color: white;
    border-radius: 16px;
    width: 85%;
    max-width: 320px;
    padding: 20px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    text-align: center;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal-container {
    transform: scale(1);
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.modal-content {
    font-size: 15px;
    color: #666;
    margin-bottom: 20px;
    line-height: 1.5;
}

.modal-buttons {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.modal-button {
    flex: 1;
    padding: 10px 0;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;
}

.modal-button-secondary {
    background-color: #f0f0f0;
    color: #666;
}

.modal-button-secondary:active {
    background-color: #e0e0e0;
}

.modal-button-primary {
    background: linear-gradient(135deg, #1E90FF, #40E0D0);
    color: white;
}

.modal-button-primary:active {
    opacity: 0.9;
}

/* 底部导航栏样式 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    display: flex;
    justify-content: space-around;
    padding: 8px 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 5px 0;
    width: 20%;
    transition: transform 0.2s ease;
}

.nav-item i {
    font-size: 24px;
    margin-bottom: 3px;
    color: #999;
    transition: color 0.3s ease;
}

.nav-item span {
    font-size: 12px;
    color: #999;
    transition: color 0.3s ease;
}

.nav-item.active i,
.nav-item.active span {
    color: #1E90FF;
}

/* 日期选择弹窗特定样式 */
.date-selector-modal {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    /* background-color: white; */ /* Ensure this is commented or removed */
    background: linear-gradient(to bottom, #FFF0F5 0%, white 30%) !important; /* Adjusted gradient for more prominent top color */
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    padding: 15px;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000; /* 与城市选择器同级，JS控制显示一个 */
    transform: translateY(100%);
    transition: transform 0.3s ease;
    max-height: 90vh; /* 允许更高的高度以容纳日历 */
    display: flex;
    flex-direction: column;
}

.date-selector-modal.active {
    transform: translateY(0);
}

.date-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-shrink: 0;
}

.date-selector-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* .close-btn 样式已在城市选择器中定义，可以共用，如果ID不同则需要单独定义 */
/* #close-date-selector 如果需要特殊样式，则在这里添加 */

.calendar-container {
    flex-grow: 1;
    overflow-y: auto; /* 如果内容多，允许日历容器本身滚动 */
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.month-nav-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #40E0D0;
    cursor: pointer;
    padding: 5px 10px;
}
.month-nav-btn:disabled {
    color: #ccc;
    cursor: not-allowed;
}

.current-month-year {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    margin-bottom: 10px;
    font-size: 13px;
    color: #666;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px; /* 日期之间的间隙 */
}

.calendar-day {
    display: flex;
    flex-direction: column; /* 允许日期下方显示节日/星期 */
    align-items: center;
    justify-content: center;
    padding: 8px 0; /* 调整padding来控制高度 */
    text-align: center;
    cursor: pointer;
    border-radius: 8px;
    font-size: 14px;
    min-height: 40px; /* 给每个日期一个最小高度 */
    transition: background-color 0.2s ease, color 0.2s ease;
}

.calendar-day.empty {
    cursor: default;
    visibility: hidden; /* 或 background-color: transparent; */
}

.calendar-day.disabled {
    color: #ccc;
    cursor: not-allowed;
    background-color: #f8f8f8;
}

.calendar-day.selected {
    background-color: #40E0D0;
    color: white;
    font-weight: bold;
}

.calendar-day.today {
    font-weight: bold;
    /* border: 1px solid #40E0D0; /* 可选：突出显示今天 */
    color: #40E0D0;
}

.calendar-day .day-number {
    font-size: 15px;
}
.calendar-day .day-info {
    font-size: 10px;
    color: #888; /* 节日/星期备注颜色 */
}
.calendar-day.selected .day-info {
    color: white; /* 选中时备注颜色 */
}

.date-selector-footer {
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
    flex-shrink: 0;
    margin-top: 10px;
}

.confirm-date-btn {
    width: 100%;
    padding: 12px 0;
    background: linear-gradient(135deg, #40E0D0, #1E90FF);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.2s ease;
}
.confirm-date-btn:hover {
    opacity: 0.9;
}

.all-cities-section {
    flex-grow: 1; /* Take remaining space */
    overflow-y: auto; /* Enable vertical scrolling for this section */
    margin-top: 10px;
    max-height: 300px; /* Or a height that fits your design, e.g., 40vh */
}

.all-cities-section h4 {
    font-size: 15px;
    color: #666;
    margin-bottom: 10px;
    position: sticky;
    top: -1px;
    background: white;
    padding-top: 10px;
    padding-bottom: 5px;
    z-index: 10;
    border-bottom: 1px solid #f0f0f0;
}

.all-city-list { /* Container for all cities, now vertical */
    display: block;
}

/* General .city-item style for the scrollable list */
.city-item {
    padding: 13px 15px;
    background-color: transparent; /* No background box */
    border-radius: 0; /* No rounded corners */
    font-size: 15px;
    color: #333;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: block;
    width: 100%;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.all-city-list .city-item:last-child {
    border-bottom: none;
}

.all-city-list .city-item:hover {
    background-color: #f9f9f9;
}

.all-city-list .city-item:active {
    background-color: #e9e9e9;
    color: #1E90FF;
}

/* Ensure hot city items remain tags - this needs to come AFTER the general .city-item rule or be more specific */
.hot-city-list .city-item {
    display: inline-block; /* Override display:block */
    width: auto; /* Override width:100% */
    text-align: center;
    padding: 7px 12px; /* Restore padding for tags */
    background-color: #f0f0f0; /* Restore background for tags */
    border-radius: 18px;    /* Restore border-radius for tags */
    font-size: 13px;        /* Restore font-size for tags */
    border-bottom: none; /* Hot city tags don't need a bottom border */
    margin-right: 8px; /* Add some right margin if they are inline-block */
    margin-bottom: 8px; /* Add some bottom margin for wrapping */
}

.hot-city-list .city-item:active {
    background-color: #1E90FF; /* Restore active state for hot city tags */
    color: white;
}

/* --- 新增：搜索覆盖页面样式 --- */
.search-overlay-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    z-index: 2000;
    display: none;
    flex-direction: column;
    opacity: 0;
    transition: opacity 0.3s ease;
    /* 防止移动端自动放大 */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

.search-overlay-page.active {
    display: flex; /* 或者 block，取决于内部布局需求 */
    opacity: 1;
}

.search-overlay-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: linear-gradient(to bottom,
        rgba(111, 123, 245, 0.08) 0%,
        rgba(255, 255, 255, 1) 100%
    );
    box-shadow: 0 1px 3px rgba(111, 123, 245, 0.1);
    flex-shrink: 0;
}

.search-overlay-back-btn,
.search-overlay-cancel-btn {
    font-size: 16px;
    color: #333;
    cursor: pointer;
    padding: 5px;
}

.search-overlay-back-btn {
    margin-right: 10px;
}

.search-overlay-cancel-btn {
    margin-left: 10px;
    font-weight: 500;
    color: #6F7BF5; /* 新主题色 */
}

.search-overlay-input-container {
    flex-grow: 1;
    display: flex;
    align-items: center;
    background-color: #f0f2f5; /* 浅灰色背景 */
    border-radius: 18px; /* 更圆的搜索框 */
    padding: 0 12px;
    height: 36px;
}

.search-overlay-input-icon {
    font-size: 14px;
    color: #888;
    margin-right: 8px;
}

/* 确保只显示一个搜索图标 */
.search-overlay-input-container .search-overlay-input-icon:not(:first-child) {
    display: none;
}

#search-overlay-input {
    flex-grow: 1;
    border: none;
    background: transparent;
    outline: none;
    font-size: 16px; /* 防止iOS自动放大 */
    color: #333;
    height: 100%;
    /* 防止移动端自动放大 */
    -webkit-text-size-adjust: 100%;
    -webkit-appearance: none;
    border-radius: 0;
}

#search-overlay-input::placeholder {
    color: #aaa;
}

.search-overlay-content {
    flex-grow: 1;
    overflow-y: auto; /* 内容区域可滚动 */
    padding: 15px;
}

.search-suggestions-section,
.search-history-section,
.search-trending-section {
    margin-bottom: 25px;
}

.search-suggestions-section h4,
.search-history-section h4,
.search-trending-section h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.clear-history-btn {
    font-size: 14px;
    color: #6F7BF5;
    cursor: pointer;
    font-weight: 400;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.clear-history-btn:hover {
    background-color: rgba(111, 123, 245, 0.1);
}

.history-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.history-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #f0f2f5;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.history-item:last-child {
    border-bottom: none;
}

.history-item:hover {
    background-color: #f8f9fa;
}

.history-item .history-text {
    flex-grow: 1;
    font-size: 14px;
    color: #333;
}

.history-item .delete-history {
    font-size: 12px;
    color: #999;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.history-item .delete-history:hover {
    color: #ff4d4f;
    background-color: rgba(255, 77, 79, 0.1);
}

/* 四角星加载动画 */
.search-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 9999;
    display: none;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.search-loading-overlay.active {
    display: flex;
}

.four-star-loader {
    position: relative;
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
}

.star-point {
    position: absolute;
    width: 16px;
    height: 16px;
    background: #6F7BF5;
    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
    animation: starRotate 1.5s ease-in-out infinite;
}

.star-point:nth-child(1) {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: #6F7BF5;
    animation-delay: 0s;
}

.star-point:nth-child(2) {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    background: #FF6B9D;
    animation-delay: 0.3s;
}

.star-point:nth-child(3) {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    background: #FFB347;
    animation-delay: 0.6s;
}

.star-point:nth-child(4) {
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    background: #6F7BF5;
    animation-delay: 0.9s;
}

@keyframes starRotate {
    0%, 100% {
        transform: translateX(-50%) translateY(-50%) scale(1) rotate(0deg);
        opacity: 1;
    }
    25% {
        transform: translateX(-50%) translateY(-50%) scale(1.2) rotate(90deg);
        opacity: 0.8;
    }
    50% {
        transform: translateX(-50%) translateY(-50%) scale(0.8) rotate(180deg);
        opacity: 0.6;
    }
    75% {
        transform: translateX(-50%) translateY(-50%) scale(1.1) rotate(270deg);
        opacity: 0.8;
    }
}

.search-loading-text {
    font-size: 16px;
    color: #6F7BF5;
    font-weight: 500;
    margin-top: 10px;
    animation: textPulse 1.5s ease-in-out infinite;
}

@keyframes textPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* 搜索结果页面样式 */
.search-results-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    z-index: 2000;
    display: none;
    flex-direction: column;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.search-results-page.active {
    display: flex;
    opacity: 1;
}

.search-results-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: linear-gradient(to bottom,
        rgba(111, 123, 245, 0.08) 0%,
        rgba(255, 255, 255, 1) 100%
    );
    box-shadow: 0 1px 3px rgba(111, 123, 245, 0.1);
    flex-shrink: 0;
}

.search-results-back-btn {
    font-size: 16px;
    color: #333;
    cursor: pointer;
    padding: 5px;
    margin-right: 10px;
}

.search-results-keyword {
    flex-grow: 1;
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.search-results-content {
    flex-grow: 1;
    overflow-y: auto;
    padding: 20px 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* 空状态插画 */
.empty-state-container {
    text-align: center;
    max-width: 300px;
    margin: 0 auto;
}

.empty-state-illustration {
    width: 200px;
    height: 200px;
    margin: 0 auto 30px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* CSS绘制的搜索插画 */
.search-illustration {
    position: relative;
    width: 120px;
    height: 120px;
}

.magnifying-glass {
    position: relative;
    width: 80px;
    height: 80px;
    border: 6px solid #E8EAFF;
    border-radius: 50%;
    background: linear-gradient(135deg, #F8F9FF, #E8EAFF);
    display: flex;
    align-items: center;
    justify-content: center;
}

.magnifying-glass::after {
    content: '';
    position: absolute;
    bottom: -15px;
    right: -15px;
    width: 30px;
    height: 6px;
    background: #D1D5FF;
    border-radius: 3px;
    transform: rotate(45deg);
}

.search-icon-inner {
    font-size: 24px;
    color: #A8B2F8;
}

/* 装饰性元素 */
.search-decoration {
    position: absolute;
    border-radius: 50%;
    background: rgba(111, 123, 245, 0.1);
    animation: floatDecoration 3s ease-in-out infinite;
}

.search-decoration:nth-child(1) {
    width: 20px;
    height: 20px;
    top: 10px;
    left: 20px;
    animation-delay: 0s;
}

.search-decoration:nth-child(2) {
    width: 16px;
    height: 16px;
    top: 30px;
    right: 15px;
    animation-delay: 1s;
}

.search-decoration:nth-child(3) {
    width: 12px;
    height: 12px;
    bottom: 20px;
    left: 30px;
    animation-delay: 2s;
}

@keyframes floatDecoration {
    0%, 100% { transform: translateY(0px); opacity: 0.6; }
    50% { transform: translateY(-10px); opacity: 1; }
}

.empty-state-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
}

.empty-state-description {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.empty-state-suggestions {
    text-align: left;
    width: 100%;
}

.empty-state-suggestions h4 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
}

.suggestion-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.suggestion-item {
    padding: 8px 0;
    font-size: 13px;
    color: #666;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
    padding-left: 20px;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #6F7BF5;
    font-weight: bold;
}

/* 搜索结果列表样式 */
.search-results-list {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.search-result-item {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f2f5;
    transition: all 0.2s ease;
    cursor: pointer;
}

.search-result-item:hover {
    box-shadow: 0 4px 16px rgba(111, 123, 245, 0.15);
    border-color: rgba(111, 123, 245, 0.2);
    transform: translateY(-2px);
}

.result-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.4;
}

.result-description {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 10px;
}

.result-type {
    display: inline-block;
    font-size: 12px;
    color: #6F7BF5;
    background: rgba(111, 123, 245, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 500;
}

.hot-keywords-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.keyword-tag {
    background-color: #e9ecef; /* 浅灰色背景 */
    color: #495057;
    padding: 6px 12px;
    border-radius: 16px; /* 胶囊形状 */
    font-size: 13px;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.keyword-tag:hover {
    background-color: #6F7BF5;
    color: white;
}

.trending-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.trending-list li {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f2f5; /* 非常浅的分割线 */
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.trending-list li:last-child {
    border-bottom: none;
}

.trending-list li:hover {
    background-color: #f8f9fa;
}

.trending-list .rank {
    font-size: 16px;
    font-weight: bold;
    color: #888;
    width: 30px; /* 固定宽度给排名数字 */
    text-align: center;
    margin-right: 12px;
}

.trending-list .rank.hot {
    color: #ff4d4f; /* 红色表示热门 */
}

.trending-list .rank.new {
    color: #ff7a45; /* 橙色表示新增/快速上升 */
}

.trending-list .title {
    flex-grow: 1;
    font-size: 14px;
    color: #333;
    line-height: 1.4;
}

.trending-list .metric {
    font-size: 12px;
    color: #aaa;
    margin-left: 10px;
    white-space: nowrap;
}
