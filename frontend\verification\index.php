<?php
session_start();

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit();
}

// 数据库连接
require_once '../db_config.php';

$user_id = $_SESSION['user_id'];

// 获取数据库连接
try {
    $pdo = getDbConnection();
} catch (Exception $e) {
    die('数据库连接失败');
}

// 获取用户信息
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: ../login/index.php');
    exit();
}

$user_username = $user['username'];
$user_avatar = $user['avatar'] ?: 'https://s1.imagehub.cc/images/2025/05/02/default-avatar.png';
$is_verified = $user['is_verified'] ?? 0;

// 获取认证记录
$stmt = $pdo->prepare("SELECT * FROM user_verifications WHERE user_id = ? ORDER BY submitted_at DESC LIMIT 1");
$stmt->execute([$user_id]);
$verification = $stmt->fetch();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实名认证 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/verification_style.css">
    <?php
    include '../includes/bottom_nav.php';
    echo renderBottomNavCSS();
    ?>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="verification-header">
        <div class="header-content">
            <a href="../profile/index.php" class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="header-title">实名认证</h1>
            <div class="header-actions">
                <a href="help.php" class="help-btn">
                    <i class="fas fa-question-circle"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- 认证状态卡片 -->
    <div class="verification-status-card">
        <div class="status-content">
            <div class="user-info">
                <div class="user-avatar">
                    <img src="<?php echo htmlspecialchars($user_avatar); ?>" alt="用户头像">
                </div>
                <div class="user-details">
                    <div class="username"><?php echo htmlspecialchars($user_username); ?></div>
                    <?php if ($is_verified): ?>
                        <div class="verification-badge verified">
                            <i class="fas fa-shield-check"></i>
                            <span>已实名认证</span>
                        </div>
                    <?php else: ?>
                        <div class="verification-badge unverified">
                            <i class="fas fa-shield-exclamation"></i>
                            <span>未实名认证</span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if ($verification): ?>
                <div class="verification-info">
                    <div class="verification-status">
                        <span class="label">认证状态</span>
                        <span class="value status-<?php echo $verification['status']; ?>">
                            <?php 
                            switch($verification['status']) {
                                case 'pending': echo '审核中'; break;
                                case 'approved': echo '已通过'; break;
                                case 'rejected': echo '已拒绝'; break;
                                default: echo '未知状态';
                            }
                            ?>
                        </span>
                    </div>
                    <div class="verification-time">
                        <span class="label">提交时间</span>
                        <span class="value"><?php echo date('Y-m-d H:i', strtotime($verification['submitted_at'])); ?></span>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- 认证说明 -->
    <div class="verification-info-section">
        <div class="section-header">
            <h2 class="section-title">认证说明</h2>
        </div>
        
        <div class="info-list">
            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-shield-check"></i>
                </div>
                <div class="info-content">
                    <div class="info-title">安全保障</div>
                    <div class="info-desc">实名认证后账户安全性更高</div>
                </div>
            </div>
            
            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="info-content">
                    <div class="info-title">专属特权</div>
                    <div class="info-desc">享受认证用户专属功能</div>
                </div>
            </div>
            
            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="info-content">
                    <div class="info-title">身份标识</div>
                    <div class="info-desc">获得认证标识，提升信任度</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 认证操作 -->
    <div class="verification-actions">
        <?php if (!$is_verified): ?>
            <?php if (!$verification || $verification['status'] == 'rejected'): ?>
                <button class="verify-btn primary" onclick="startVerification()">
                    <i class="fas fa-id-card"></i>
                    <span>开始实名认证</span>
                </button>
            <?php elseif ($verification['status'] == 'pending'): ?>
                <button class="verify-btn disabled">
                    <i class="fas fa-clock"></i>
                    <span>审核中，请耐心等待</span>
                </button>
            <?php endif; ?>
        <?php else: ?>
            <div class="verified-message">
                <i class="fas fa-check-circle"></i>
                <span>您已完成实名认证</span>
            </div>
        <?php endif; ?>
    </div>

    <!-- 认证须知 -->
    <div class="verification-notice">
        <div class="section-header">
            <h2 class="section-title">认证须知</h2>
        </div>
        
        <div class="notice-content">
            <div class="notice-item">
                <span class="notice-number">1</span>
                <span class="notice-text">请确保身份证信息真实有效</span>
            </div>
            <div class="notice-item">
                <span class="notice-number">2</span>
                <span class="notice-text">上传的身份证照片需清晰完整</span>
            </div>
            <div class="notice-item">
                <span class="notice-number">3</span>
                <span class="notice-text">审核时间为1-3个工作日</span>
            </div>
            <div class="notice-item">
                <span class="notice-number">4</span>
                <span class="notice-text">个人信息将严格保密</span>
            </div>
        </div>
    </div>

    <!-- 认证表单弹窗 -->
    <div class="verification-modal" id="verificationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>实名认证</h3>
                <button class="close-btn" onclick="hideVerificationModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="verificationForm">
                    <div class="form-group">
                        <label>真实姓名</label>
                        <input type="text" name="real_name" placeholder="请输入真实姓名" required>
                    </div>
                    <div class="form-group">
                        <label>身份证号</label>
                        <input type="text" name="id_card" placeholder="请输入身份证号" required>
                    </div>
                    <div class="form-group">
                        <label>身份证正面照片</label>
                        <div class="upload-area" onclick="uploadImage('front')">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>点击上传身份证正面</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>身份证背面照片</label>
                        <div class="upload-area" onclick="uploadImage('back')">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>点击上传身份证背面</span>
                        </div>
                    </div>
                    <button type="submit" class="submit-btn">提交认证</button>
                </form>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <?php echo renderBottomNav('profile'); ?>

    <script src="js/verification.js"></script>
</body>
</html>
