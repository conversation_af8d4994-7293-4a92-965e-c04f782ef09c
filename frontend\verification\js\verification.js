/**
 * 实名认证页面交互脚本
 */

// 显示认证弹窗
function startVerification() {
    const modal = document.getElementById('verificationModal');
    if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        
        // 添加显示动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }
}

// 隐藏认证弹窗
function hideVerificationModal() {
    const modal = document.getElementById('verificationModal');
    if (modal) {
        modal.classList.remove('show');
        
        setTimeout(() => {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }, 300);
    }
}

// 上传图片
function uploadImage(type) {
    // 创建文件输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.style.display = 'none';
    
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            // 检查文件大小（限制5MB）
            if (file.size > 5 * 1024 * 1024) {
                showToast('图片大小不能超过5MB');
                return;
            }
            
            // 检查文件类型
            if (!file.type.startsWith('image/')) {
                showToast('请选择图片文件');
                return;
            }
            
            // 这里可以添加图片上传逻辑
            showToast(`${type === 'front' ? '身份证正面' : '身份证背面'}图片已选择`);
            
            // 更新上传区域显示
            const uploadArea = document.querySelector(`[onclick="uploadImage('${type}')"]`);
            if (uploadArea) {
                uploadArea.innerHTML = `
                    <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                    <span style="color: var(--success-color);">${type === 'front' ? '身份证正面' : '身份证背面'}已选择</span>
                `;
                uploadArea.style.borderColor = 'var(--success-color)';
                uploadArea.style.background = 'rgba(78, 205, 196, 0.05)';
            }
        }
    };
    
    document.body.appendChild(input);
    input.click();
    document.body.removeChild(input);
}

// Toast提示函数
function showToast(message, duration = 3000) {
    // 移除已存在的toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // 创建新的toast
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    
    // 添加样式
    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: var(--bg-card);
        color: var(--text-primary);
        padding: 16px 24px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        font-size: 14px;
        font-weight: 500;
        max-width: 90%;
        text-align: center;
        border: 1px solid var(--border-color);
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
    }, 10);
    
    // 自动隐藏
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// 表单验证
function validateForm() {
    const form = document.getElementById('verificationForm');
    const realName = form.real_name.value.trim();
    const idCard = form.id_card.value.trim();
    
    // 验证姓名
    if (!realName) {
        showToast('请输入真实姓名');
        return false;
    }
    
    if (realName.length < 2 || realName.length > 10) {
        showToast('姓名长度应在2-10个字符之间');
        return false;
    }
    
    // 验证身份证号
    if (!idCard) {
        showToast('请输入身份证号');
        return false;
    }
    
    // 简单的身份证号格式验证
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idCardRegex.test(idCard)) {
        showToast('请输入正确的身份证号格式');
        return false;
    }
    
    return true;
}

// 提交认证表单
function submitVerification(e) {
    e.preventDefault();
    
    if (!validateForm()) {
        return;
    }
    
    // 显示提交确认
    if (!confirm('确定要提交实名认证申请吗？提交后无法修改。')) {
        return;
    }
    
    // 这里可以添加实际的提交逻辑
    showToast('认证申请已提交，请等待审核');
    
    // 模拟提交过程
    setTimeout(() => {
        hideVerificationModal();
        // 可以刷新页面或更新状态
        location.reload();
    }, 2000);
}

// 返回按钮功能
function initBackButton() {
    const backBtn = document.querySelector('.back-btn');
    if (backBtn) {
        backBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 添加点击动画
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = '';
                // 返回上一页
                window.history.back();
            }, 150);
        });
    }
}

// 帮助按钮功能
function initHelpButton() {
    const helpBtn = document.querySelector('.help-btn');
    if (helpBtn) {
        helpBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showToast('认证帮助功能即将上线');
        });
    }
}

// 弹窗外部点击关闭
function initModalClose() {
    const modal = document.getElementById('verificationModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                hideVerificationModal();
            }
        });
    }
}

// 表单提交事件
function initFormSubmit() {
    const form = document.getElementById('verificationForm');
    if (form) {
        form.addEventListener('submit', submitVerification);
    }
}

// 信息卡片点击效果
function initInfoItems() {
    const infoItems = document.querySelectorAll('.info-item');
    
    infoItems.forEach(item => {
        item.addEventListener('click', function() {
            const title = this.querySelector('.info-title').textContent;
            showToast(`${title}功能详情即将上线`);
            
            // 添加点击动画
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('实名认证页面加载完成');
    
    // 初始化各种交互功能
    initBackButton();
    initHelpButton();
    initModalClose();
    initFormSubmit();
    initInfoItems();
    
    // 添加页面加载动画
    const elements = document.querySelectorAll('.verification-status-card, .verification-info-section, .verification-actions, .verification-notice');
    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.6s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    console.log('实名认证交互功能初始化完成');
});

// 键盘事件处理
document.addEventListener('keydown', function(e) {
    // ESC键关闭弹窗
    if (e.key === 'Escape') {
        hideVerificationModal();
    }
});

// 导出函数供全局使用
window.verification = {
    startVerification,
    hideVerificationModal,
    uploadImage,
    showToast
};
