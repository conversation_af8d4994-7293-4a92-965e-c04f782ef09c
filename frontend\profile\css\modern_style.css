/**
 * 现代化个人中心样式
 * 年轻化、精美、交互性强的设计
 */

/* ===== CSS变量定义 ===== */
:root {
    /* 主题色系 - 年轻化配色 */
    --primary-color: #6F7BF5;
    --secondary-color: #AFFBF2;
    --accent-color: #40E0D0;
    --warning-color: #FF6B6B;
    --success-color: #4ECDC4;
    --bright-yellow: #FFD166;
    --bright-orange: #FF8A65;
    --watermelon-red: #FF5722;
    --purple-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --blue-gradient: linear-gradient(135deg, #6F7BF5 0%, #40E0D0 100%);
    --warm-gradient: linear-gradient(135deg, #FF8A65 0%, #FF5722 100%);
    --cool-gradient: linear-gradient(135deg, #AFFBF2 0%, #40E0D0 100%);

    /* 背景和文字 */
    --bg-primary: #F8F9FA;
    --bg-secondary: #FFFFFF;
    --bg-card: #FFFFFF;
    --text-primary: #2C3E50;
    --text-secondary: #7F8C8D;
    --text-light: #BDC3C7;
    --border-color: #E9ECEF;

    /* 阴影和圆角 */
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-strong: 0 8px 32px rgba(0, 0, 0, 0.16);
    --shadow-colored: 0 8px 32px rgba(111, 123, 245, 0.2);
    --border-radius: 16px;
    --border-radius-small: 12px;
    --border-radius-large: 24px;
    --border-radius-xl: 32px;

    /* 动画 */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== 全局样式重置 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    padding-bottom: 80px;
    overflow-x: hidden;
}

a {
    text-decoration: none;
    color: inherit;
}

/* ===== 现代化用户卡片 ===== */
.modern-user-card {
    position: relative;
    background: var(--bg-card);
    margin: 0 16px 24px;
    border-radius: var(--border-radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
}

/* 渐变背景头部 */
.card-header {
    position: relative;
    height: 200px;
    background: var(--blue-gradient);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

/* 动态背景装饰 */
.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* 顶部操作按钮 */
.modern-card-actions {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 12px;
    z-index: 10;
}

.modern-action-btn {
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.modern-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

/* 现代化头像 */
.modern-avatar-container {
    position: relative;
    z-index: 5;
    margin-bottom: 16px;
}

.modern-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 4px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
    position: relative;
    box-shadow: var(--shadow-medium);
    transition: var(--transition);
}

.modern-avatar:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-strong);
}

.modern-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 用户信息 */
.modern-user-info {
    text-align: center;
    color: white;
    z-index: 5;
    position: relative;
}

.modern-user-name {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.modern-user-id-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;
}

.modern-user-id {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.2);
    padding: 6px 12px;
    border-radius: var(--border-radius-small);
    backdrop-filter: blur(10px);
}

.modern-copy-btn {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: var(--border-radius-small);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.modern-copy-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 用户铭牌 */
.modern-nameplate {
    position: absolute;
    bottom: -16px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 6;
}

.modern-nameplate img {
    height: 32px;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* 卡片内容区域 */
.card-content {
    padding: 24px;
    background: var(--bg-card);
}

/* 统计信息 */
.modern-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 24px;
}

.stat-item {
    text-align: center;
    padding: 16px;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.stat-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* 用户简介 */
.modern-bio {
    background: var(--bg-primary);
    padding: 16px;
    border-radius: var(--border-radius);
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.6;
    border-left: 4px solid var(--accent-color);
}

/* ===== 现代化会员卡片 ===== */
.modern-member-card {
    margin: 0 16px 24px;
    background: var(--bg-card);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    position: relative;
}

.member-gradient-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--warm-gradient);
    opacity: 0.1;
}

.modern-member-content {
    position: relative;
    padding: 20px;
    z-index: 2;
}

.member-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.modern-member-icon {
    width: 48px;
    height: 48px;
    background: var(--warm-gradient);
    border-radius: var(--border-radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    box-shadow: var(--shadow-light);
}

.modern-member-icon img {
    width: 28px;
    height: 28px;
    filter: brightness(0) invert(1);
}

.member-info {
    flex: 1;
}

.modern-member-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.modern-member-level {
    font-size: 14px;
    color: var(--bright-orange);
    font-weight: 600;
}

.modern-member-btn {
    background: var(--warm-gradient);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius-large);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.modern-member-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* 会员权益 */
.modern-benefits {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
}

.modern-benefit-item {
    text-align: center;
    padding: 12px 8px;
    background: rgba(255, 138, 101, 0.1);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.modern-benefit-item:hover {
    background: rgba(255, 138, 101, 0.2);
    transform: translateY(-2px);
}

.modern-benefit-icon {
    width: 36px;
    height: 36px;
    background: var(--warm-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    color: white;
    font-size: 16px;
}

.modern-benefit-text {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* ===== 现代化功能网格 ===== */
.modern-feature-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin: 0 16px 24px;
}

.modern-feature-item {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 20px 12px;
    text-align: center;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
    position: relative;
    overflow: hidden;
}

.modern-feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--blue-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.modern-feature-item:hover::before {
    transform: scaleX(1);
}

.modern-feature-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-colored);
}

.modern-feature-icon {
    width: 48px;
    height: 48px;
    background: var(--blue-gradient);
    border-radius: var(--border-radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    transition: var(--transition);
}

.modern-feature-item:hover .modern-feature-icon {
    transform: scale(1.1) rotate(5deg);
}

.modern-feature-icon i {
    color: white;
    font-size: 20px;
}

.modern-feature-icon img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
}

.modern-feature-label {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-primary);
}

/* ===== 现代化菜单列表 ===== */
.modern-menu-section {
    margin: 0 16px 24px;
}

.modern-menu-group {
    background: var(--bg-card);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    margin-bottom: 16px;
}

.modern-menu-item {
    display: flex;
    align-items: center;
    padding: 20px;
    transition: var(--transition);
    position: relative;
    border-bottom: 1px solid var(--border-color);
}

.modern-menu-item:last-child {
    border-bottom: none;
}

.modern-menu-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--blue-gradient);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.modern-menu-item:hover::before {
    transform: scaleY(1);
}

.modern-menu-item:hover {
    background: var(--bg-primary);
    transform: translateX(4px);
}

.modern-menu-icon {
    width: 48px;
    height: 48px;
    background: var(--blue-gradient);
    border-radius: var(--border-radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    transition: var(--transition);
}

.modern-menu-item:hover .modern-menu-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-medium);
}

.modern-menu-icon img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
}

.modern-menu-content {
    flex: 1;
}

.modern-menu-label {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.modern-menu-desc {
    font-size: 12px;
    color: var(--text-secondary);
}

.modern-menu-arrow {
    color: var(--text-light);
    font-size: 16px;
    transition: var(--transition);
}

.modern-menu-item:hover .modern-menu-arrow {
    color: var(--primary-color);
    transform: translateX(4px);
}

/* ===== 现代化Toast提示 ===== */
.modern-toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-card);
    color: var(--text-primary);
    padding: 16px 24px;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-strong);
    z-index: 10000;
    display: none;
    font-size: 14px;
    font-weight: 500;
    max-width: 90%;
    text-align: center;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.modern-toast.show {
    display: block;
    animation: toastShow 0.3s ease;
}

@keyframes toastShow {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* ===== 额外的现代化动画 ===== */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 页面加载动画 */
.modern-user-card {
    animation: slideInUp 0.6s ease-out;
}

.modern-member-card {
    animation: slideInUp 0.6s ease-out 0.1s both;
}

.modern-feature-grid {
    animation: slideInUp 0.6s ease-out 0.2s both;
}

.modern-menu-section {
    animation: slideInUp 0.6s ease-out 0.3s both;
}

/* 悬停时的脉冲效果 */
.modern-feature-item:hover .modern-feature-icon {
    animation: pulse 1s infinite;
}

/* 点击时的涟漪效果 */
.modern-menu-item {
    position: relative;
    overflow: hidden;
}

.modern-menu-item::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(111, 123, 245, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.modern-menu-item:active::after {
    width: 300px;
    height: 300px;
}

/* 统计数字动画 */
.stat-value {
    transition: var(--transition);
}

.stat-item:hover .stat-value {
    color: var(--primary-color);
    transform: scale(1.1);
}

/* 头像悬停效果 */
.modern-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.modern-avatar:hover::before {
    transform: translateX(100%);
}

/* 按钮点击反馈 */
.modern-action-btn:active,
.modern-copy-btn:active,
.modern-member-btn:active {
    transform: scale(0.95);
}

/* 加载状态 */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .modern-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }

    .modern-feature-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;
        margin: 0 12px 20px;
    }

    .modern-menu-section {
        margin: 0 12px 20px;
    }

    .modern-user-card {
        margin: 0 12px 20px;
    }

    .modern-member-card {
        margin: 0 12px 20px;
    }
}

@media (max-width: 480px) {
    .card-header {
        height: 180px;
    }

    .modern-avatar {
        width: 80px;
        height: 80px;
    }

    .modern-user-name {
        font-size: 20px;
    }

    .modern-benefits {
        grid-template-columns: repeat(2, 1fr);
    }

    .modern-feature-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}
