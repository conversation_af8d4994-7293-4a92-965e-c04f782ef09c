/**
 * 现代化个人中心样式
 * 年轻化、精美、交互性强的设计
 */

/* ===== CSS变量定义 ===== */
:root {
    /* 主题色系 - 年轻化配色 */
    --primary-color: #6F7BF5;
    --secondary-color: #AFFBF2;
    --accent-color: #40E0D0;
    --warning-color: #FF6B6B;
    --success-color: #4ECDC4;
    --bright-yellow: #FFD166;
    --bright-orange: #FF8A65;
    --watermelon-red: #FF5722;
    --purple-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --blue-gradient: linear-gradient(135deg, #6F7BF5 0%, #40E0D0 100%);
    --warm-gradient: linear-gradient(135deg, #FF8A65 0%, #FF5722 100%);
    --cool-gradient: linear-gradient(135deg, #AFFBF2 0%, #40E0D0 100%);

    /* 背景和文字 */
    --bg-primary: #F8F9FA;
    --bg-secondary: #FFFFFF;
    --bg-card: #FFFFFF;
    --text-primary: #2C3E50;
    --text-secondary: #7F8C8D;
    --text-light: #BDC3C7;
    --border-color: #E9ECEF;

    /* 阴影和圆角 */
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-strong: 0 8px 32px rgba(0, 0, 0, 0.16);
    --shadow-colored: 0 8px 32px rgba(111, 123, 245, 0.2);
    --border-radius: 16px;
    --border-radius-small: 12px;
    --border-radius-large: 24px;
    --border-radius-xl: 32px;

    /* 动画 */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== 全局样式重置 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    padding: 0;
    margin: 0;
    padding-bottom: 80px;
    overflow-x: hidden;
    width: 100%;
}

/* 确保HTML也是全屏 */
html {
    margin: 0;
    padding: 0;
    width: 100%;
    overflow-x: hidden;
}

/* 强制全屏显示 */
* {
    box-sizing: border-box;
}

html, body {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
}

a {
    text-decoration: none;
    color: inherit;
}

/* ===== 简洁全屏用户卡片 ===== */
.modern-user-card {
    position: relative;
    background: var(--bg-card);
    margin: 0;
    border-radius: 0 0 24px 24px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 简洁渐变背景头部 */
.card-header {
    position: relative;
    height: 240px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

/* 简洁背景装饰 */
.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 30%, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

/* 简洁操作按钮 */
.modern-card-actions {
    position: absolute;
    top: 16px;
    right: 16px;
    display: flex;
    gap: 8px;
    z-index: 10;
}

.modern-action-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    transition: all 0.2s ease;
    border: none;
}

.modern-action-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.1);
}

/* 重新设计的用户信息布局 */
.modern-user-layout {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 0 8px;
    z-index: 5;
    position: relative;
}

/* 头像在左侧 */
.modern-avatar-container {
    position: relative;
    flex-shrink: 0;
    margin-top: 4px; /* 微调对齐 */
}

.modern-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.8);
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.modern-avatar:hover {
    transform: scale(1.05);
}

.modern-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 用户信息在右侧 */
.modern-user-info {
    flex: 1;
    color: white;
    text-align: left;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.modern-user-name {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    line-height: 1.2;
}

/* 状态图标容器 */
.modern-status-icons {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

/* 通用图标样式 */
.member-icon, .verified-icon, .companion-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    text-decoration: none;
}

.member-icon img, .verified-icon img, .companion-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: all 0.2s ease;
}

/* 未激活状态 - 灰色滤镜 */
.member-icon.inactive img,
.verified-icon.inactive img,
.companion-icon.inactive img {
    filter: grayscale(100%) brightness(0.7) opacity(0.6);
}

/* 激活状态 - 原色显示 */
.member-icon.active img,
.verified-icon.active img,
.companion-icon.active img {
    filter: none;
}

/* 悬停效果 */
.member-icon:hover,
.verified-icon:hover,
.companion-icon:hover {
    transform: scale(1.1);
}

/* 激活状态的发光效果 */
.member-icon.active:hover img {
    filter: drop-shadow(0 0 6px rgba(255, 215, 0, 0.6));
}

.verified-icon.active:hover img {
    filter: drop-shadow(0 0 6px rgba(111, 123, 245, 0.6));
}

.companion-icon.active:hover img {
    filter: drop-shadow(0 0 6px rgba(78, 205, 196, 0.6));
}

.modern-user-id-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.modern-user-id {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.95);
    background: rgba(255, 255, 255, 0.15);
    padding: 6px 12px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-copy-btn {
    width: 28px;
    height: 28px;
    background: rgba(255, 255, 255, 0.15);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 12px;
}

.modern-copy-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.1);
}

/* 用户铭牌 */
.modern-nameplate {
    position: absolute;
    bottom: -16px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 6;
}

.modern-nameplate img {
    height: 32px;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* 简洁卡片内容区域 */
.card-content {
    padding: 20px 0;
    background: var(--bg-card);
}

/* 重新设计的统计信息 */
.modern-stats {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 20px;
    background: var(--bg-primary);
    border-radius: 16px;
    padding: 20px 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stat-item {
    text-align: center;
    flex: 1;
    transition: all 0.2s ease;
    cursor: pointer;
    padding: 8px;
    border-radius: 12px;
}

.stat-item:hover {
    background: rgba(111, 123, 245, 0.1);
    transform: translateY(-2px);
}

.stat-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
    display: block;
}

.stat-label {
    font-size: 13px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* 用户简介 */
.modern-bio {
    background: var(--bg-primary);
    padding: 16px;
    border-radius: var(--border-radius);
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.6;
    border-left: 4px solid var(--accent-color);
}

/* 会员卡片已删除，改为昵称后的会员图标 */

/* ===== 简洁功能网格容器 ===== */
.modern-feature-container {
    margin: 0 0 20px 0;
    background: var(--bg-card);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid var(--border-color);
}

.modern-feature-grid {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

.modern-feature-item {
    background: transparent;
    border-radius: 12px;
    padding: 16px 8px;
    text-align: center;
    transition: all 0.2s ease;
    border: none;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.modern-feature-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(111, 123, 245, 0.1);
    border-color: var(--primary-color);
    background: var(--bg-card);
}

/* 不同颜色的图标背景 */
.modern-feature-icon {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    transition: all 0.2s ease;
}

/* 钱包 - 绿色 */
.modern-feature-item:nth-child(1) .modern-feature-icon {
    background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
}

/* 订单 - 蓝色 */
.modern-feature-item:nth-child(2) .modern-feature-icon {
    background: linear-gradient(135deg, #6F7BF5 0%, #5A67D8 100%);
}

/* 券包 - 橙色 */
.modern-feature-item:nth-child(3) .modern-feature-icon {
    background: linear-gradient(135deg, #FF8A65 0%, #FF7043 100%);
}

/* 活动 - 紫色 */
.modern-feature-item:nth-child(4) .modern-feature-icon {
    background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
}

.modern-feature-item:hover .modern-feature-icon {
    transform: scale(1.1);
}

.modern-feature-icon i {
    color: white;
    font-size: 18px;
}

.modern-feature-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-primary);
}

/* ===== 简洁菜单列表 ===== */
.modern-menu-section {
    margin: 0 0 20px 0;
}

.modern-menu-group {
    background: var(--bg-card);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 12px;
    border: 1px solid var(--border-color);
}

.modern-menu-item {
    display: flex;
    align-items: center;
    padding: 16px;
    transition: all 0.2s ease;
    position: relative;
    border-bottom: 1px solid var(--border-color);
}

.modern-menu-item:last-child {
    border-bottom: none;
}

.modern-menu-item:hover {
    background: var(--bg-primary);
}

/* 不同颜色的菜单图标 */
.modern-menu-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    transition: all 0.2s ease;
}

/* 会员中心 - 金色 */
.modern-menu-item:nth-child(1) .modern-menu-icon {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

/* 签到 - 绿色 */
.modern-menu-item:nth-child(2) .modern-menu-icon {
    background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
}

/* 商城 - 橙色 */
.modern-menu-item:nth-child(3) .modern-menu-icon {
    background: linear-gradient(135deg, #FF8A65 0%, #FF7043 100%);
}

/* 公会 - 蓝色 */
.modern-menu-group:nth-child(2) .modern-menu-item:nth-child(1) .modern-menu-icon {
    background: linear-gradient(135deg, #6F7BF5 0%, #5A67D8 100%);
}

/* 作品 - 紫色 */
.modern-menu-group:nth-child(2) .modern-menu-item:nth-child(2) .modern-menu-icon {
    background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
}

/* 组局 - 青色 */
.modern-menu-group:nth-child(2) .modern-menu-item:nth-child(3) .modern-menu-icon {
    background: linear-gradient(135deg, #40E0D0 0%, #00CED1 100%);
}

/* 创作者中心 - 金色 */
.modern-menu-group:nth-child(3) .modern-menu-item:nth-child(1) .modern-menu-icon {
    background: linear-gradient(135deg, #FFD166 0%, #FF8A65 100%);
}

.modern-menu-icon i {
    color: white;
    font-size: 16px;
}

.modern-menu-content {
    flex: 1;
}

.modern-menu-label {
    font-size: 15px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.modern-menu-desc {
    font-size: 12px;
    color: var(--text-secondary);
}

.modern-menu-arrow {
    color: var(--text-light);
    font-size: 14px;
    transition: all 0.2s ease;
}

.modern-menu-item:hover .modern-menu-arrow {
    color: var(--primary-color);
    transform: translateX(2px);
}

/* ===== 现代化Toast提示 ===== */
.modern-toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-card);
    color: var(--text-primary);
    padding: 16px 24px;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-strong);
    z-index: 10000;
    display: none;
    font-size: 14px;
    font-weight: 500;
    max-width: 90%;
    text-align: center;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.modern-toast.show {
    display: block;
    animation: toastShow 0.3s ease;
}

@keyframes toastShow {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* ===== 简洁动画效果 ===== */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 简单的页面加载动画 */
.modern-user-card {
    animation: slideInUp 0.4s ease-out;
}

.modern-member-card {
    animation: slideInUp 0.4s ease-out 0.1s both;
}

.modern-feature-grid {
    animation: slideInUp 0.4s ease-out 0.2s both;
}

.modern-menu-section {
    animation: slideInUp 0.4s ease-out 0.3s both;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .modern-stats {
        padding: 16px 12px;
    }

    .modern-feature-container {
        margin: 0 0 20px 0;
        padding: 16px;
    }

    .modern-menu-section {
        margin: 0 0 20px 0;
    }

    .modern-user-card {
        margin: 0 0 20px 0;
    }
}

@media (max-width: 480px) {
    .card-header {
        height: 200px;
    }

    .modern-user-layout {
        gap: 12px;
        padding: 0 8px;
    }

    .modern-avatar {
        width: 70px;
        height: 70px;
    }

    .modern-user-name {
        font-size: 18px;
        margin-bottom: 6px;
    }

    .modern-user-id {
        font-size: 12px;
        padding: 4px 10px;
    }

    .modern-copy-btn {
        width: 24px;
        height: 24px;
        font-size: 10px;
    }

    .modern-status-icons {
        gap: 6px;
        margin-top: 6px;
    }

    .member-icon, .verified-icon, .companion-icon {
        width: 18px;
        height: 18px;
    }

    .modern-feature-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}
