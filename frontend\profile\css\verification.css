/* 实名认证页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

/* 顶部导航栏 */
.header-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 56px;
    background-color: #40E0D0;
    display: flex;
    align-items: center;
    padding: 0 16px;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.back-button {
    color: white;
    font-size: 20px;
    text-decoration: none;
    margin-right: 16px;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.back-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.header-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
}

/* 主容器 */
.container {
    margin-top: 56px;
    padding: 20px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* 认证状态卡片 */
.verification-status {
    background: white;
    border-radius: 16px;
    padding: 30px 20px;
    text-align: center;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.status-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.verification-status.verified .status-icon {
    color: #06D6A0;
}

.verification-status.pending .status-icon {
    color: #FFD166;
}

.verification-status.rejected .status-icon {
    color: #FF6B6B;
}

.verification-status h2 {
    font-size: 24px;
    margin-bottom: 8px;
    font-weight: 600;
}

.verification-status.verified h2 {
    color: #06D6A0;
}

.verification-status.pending h2 {
    color: #FFD166;
}

.verification-status.rejected h2 {
    color: #FF6B6B;
}

.verification-status p {
    color: #666;
    margin-bottom: 20px;
}

/* 用户信息 */
.user-info {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px;
    margin: 20px 0;
    text-align: left;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item .label {
    font-weight: 500;
    color: #666;
}

.info-item .value {
    color: #333;
    font-weight: 500;
}

/* 拒绝原因 */
.rejection-reason {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    text-align: left;
}

.rejection-reason h4 {
    color: #e53e3e;
    margin-bottom: 8px;
}

.rejection-reason p {
    color: #744210;
    margin: 0;
}

/* 提示信息 */
.tips {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 12px;
    margin: 16px 0;
}

.tips p {
    color: #0369a1;
    margin: 0;
    font-size: 14px;
}

.tips i {
    margin-right: 8px;
}

/* 认证介绍 */
.verification-intro {
    background: white;
    border-radius: 16px;
    padding: 30px 20px;
    text-align: center;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.intro-icon {
    font-size: 48px;
    color: #40E0D0;
    margin-bottom: 16px;
}

.verification-intro h2 {
    font-size: 24px;
    margin-bottom: 8px;
    color: #333;
}

.verification-intro p {
    color: #666;
    margin-bottom: 20px;
}

.benefits {
    text-align: left;
    margin-top: 20px;
}

.benefits h4 {
    color: #333;
    margin-bottom: 12px;
    font-size: 16px;
}

.benefits ul {
    list-style: none;
    padding: 0;
}

.benefits li {
    display: flex;
    align-items: center;
    padding: 6px 0;
    color: #666;
}

.benefits li i {
    color: #06D6A0;
    margin-right: 8px;
    font-size: 14px;
}

/* 认证表单 */
.verification-form {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.verification-form h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 18px;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #40E0D0;
}

.privacy-notice {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin: 16px 0;
    text-align: center;
}

.privacy-notice p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.privacy-notice i {
    color: #40E0D0;
    margin-right: 6px;
}

/* 按钮样式 */
.btn-primary {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #40E0D0, #06D6A0);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(64, 224, 208, 0.3);
}

.btn-primary:active {
    transform: translateY(0);
}

.action-buttons {
    margin-top: 20px;
}

/* 消息提示 */
.error-message,
.success-message {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.error-message {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    color: #e53e3e;
}

.success-message {
    background: #f0fff4;
    border: 1px solid #9ae6b4;
    color: #38a169;
}

.error-message i,
.success-message i {
    margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .container {
        padding: 16px;
    }

    .verification-status,
    .verification-intro,
    .verification-form {
        padding: 20px 16px;
    }

    .status-icon,
    .intro-icon {
        font-size: 40px;
    }

    .verification-status h2,
    .verification-intro h2 {
        font-size: 20px;
    }

    .audit-info {
        padding: 15px;
        margin: 15px 0;
    }

    .status-badge {
        font-size: 13px;
        padding: 6px 12px;
    }

    .rejection-reason {
        padding: 12px;
    }
}

/* 审核信息样式 */
.audit-info {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    border-left: 4px solid #40E0D0;
}

.audit-info h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.audit-info h4 i {
    color: #40E0D0;
    margin-right: 8px;
}

.audit-status {
    margin-bottom: 15px;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.status-badge.status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.status-approved {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.status-rejected {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.audit-desc {
    color: #666;
    line-height: 1.6;
    margin-bottom: 10px;
}

.audit-time {
    color: #666;
    font-size: 14px;
    margin-top: 10px;
}

.audit-time i {
    color: #40E0D0;
    margin-right: 6px;
}

.rejection-reason {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.rejection-reason h5 {
    color: #c53030;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 600;
}

.rejection-reason h5 i {
    margin-right: 6px;
}

.reason-text {
    color: #2d3748;
    line-height: 1.6;
    margin: 0;
    background: white;
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #fc8181;
}

/* 状态图标动画 */
.status-badge i {
    animation: pulse 2s infinite;
}

.status-badge.status-approved i {
    animation: checkmark 0.6s ease-in-out;
}

.status-badge.status-rejected i {
    animation: shake 0.5s ease-in-out;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes checkmark {
    0% {
        transform: scale(0);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-3px);
    }
    75% {
        transform: translateX(3px);
    }
}

/* 图片上传组件样式 */
.upload-container {
    margin: 10px 0;
}

.upload-area {
    border: 2px dashed #40E0D0;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(64, 224, 208, 0.05);
}

.upload-area:hover {
    border-color: #20B2AA;
    background: rgba(64, 224, 208, 0.1);
    transform: translateY(-2px);
}

.upload-area i {
    font-size: 48px;
    color: #40E0D0;
    margin-bottom: 15px;
    display: block;
}

.upload-area p {
    margin: 10px 0 5px 0;
    font-size: 16px;
    color: #333;
    font-weight: 500;
}

.upload-area small {
    color: #666;
    font-size: 12px;
}

.upload-preview {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.upload-preview img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
}

.remove-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: #ff4757;
    color: white;
    transform: scale(1.1);
}

.remove-btn i {
    font-size: 14px;
}

/* Toast 通知样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 10px;
    transform: translateX(400px);
    transition: all 0.3s ease;
    z-index: 10000;
    min-width: 300px;
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    border-left: 4px solid #28a745;
}

.toast-error {
    border-left: 4px solid #dc3545;
}

.toast-info {
    border-left: 4px solid #17a2b8;
}

.toast i {
    font-size: 18px;
}

.toast-success i {
    color: #28a745;
}

.toast-error i {
    color: #dc3545;
}

.toast-info i {
    color: #17a2b8;
}
