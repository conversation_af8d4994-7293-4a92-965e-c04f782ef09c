<?php
/**
 * 左侧菜单组件
 * 趣玩星球管理后台
 */

// 获取当前页面文件名
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// 获取待审核数量
try {
    if (!isset($pdo)) {
        require_once __DIR__ . '/../db_config.php';
        $pdo = getDbConnection();
    }

    // 实名审核待审核数量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM real_name_verification WHERE status = 'pending'");
    $pending_count = $stmt->fetch()['count'] ?? 0;

    // 申诉待审核数量
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_appeals WHERE status = 'pending'");
        $appeal_pending_count = $stmt->fetch()['count'] ?? 0;
    } catch (Exception $e) {
        $appeal_pending_count = 0;
    }
} catch (Exception $e) {
    // 如果数据库查询失败，使用默认值
    $pending_count = 0;
    $appeal_pending_count = 0;
}

// 获取管理员信息
$admin_name = $_SESSION['admin_name'] ?? '管理员';
$admin_role = $_SESSION['admin_role'] ?? '系统管理员';
?>

<aside class="sidebar">
    <div class="sidebar-header">
        <div class="logo">
            <div class="logo-icon">
                <img src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="趣玩星球" class="logo-image">
            </div>
            <div class="logo-text">
                <h2>趣玩星球</h2>
                <p>管理后台</p>
            </div>
        </div>
    </div>

    <nav class="sidebar-nav">
        <ul class="nav-list">
            <!-- 首页 -->
            <li class="nav-item <?php echo $current_page === 'home.php' ? 'active' : ''; ?>">
                <a href="/houtai_backup/home.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span class="nav-link-text">首页</span>
                </a>
            </li>

            <!-- 用户管理 -->
            <li class="nav-item has-submenu <?php echo ($current_dir === 'user_management' || $current_dir === 'verification' || $current_dir === 'appeal') ? 'active open' : ''; ?>">
                <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                    <i class="fas fa-users"></i>
                    <span class="nav-link-text">用户管理</span>
                    <i class="fas fa-chevron-right submenu-arrow"></i>
                </a>
                <ul class="submenu <?php echo ($current_dir === 'user_management' || $current_dir === 'verification' || $current_dir === 'appeal') ? 'show' : ''; ?>">
                    <li class="submenu-item <?php echo $current_dir === 'user_management' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/user_management/index.php" class="submenu-link">
                            <i class="fas fa-user-friends"></i>
                            <span class="submenu-link-text">用户管理</span>
                        </a>
                    </li>
                    <li class="submenu-item <?php echo $current_dir === 'verification' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/verification/index.php" class="submenu-link">
                            <i class="fas fa-user-shield"></i>
                            <span class="submenu-link-text">实名审核</span>
                            <?php if ($pending_count > 0): ?>
                                <span class="nav-badge"><?php echo $pending_count; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="submenu-item <?php echo $current_dir === 'appeal' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/appeal/index.php" class="submenu-link">
                            <i class="fas fa-file-alt"></i>
                            <span class="submenu-link-text">申诉审核</span>
                            <?php if ($appeal_pending_count > 0): ?>
                                <span class="nav-badge"><?php echo $appeal_pending_count; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="submenu-item <?php echo $current_page === 'user_profiles.php' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/user_management/user_profiles.php" class="submenu-link">
                            <i class="fas fa-user-chart"></i>
                            <span class="submenu-link-text">用户画像</span>
                        </a>
                    </li>
                    <li class="submenu-item <?php echo $current_page === 'verification_code_logs.php' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/user_management/verification_code_logs.php" class="submenu-link">
                            <i class="fas fa-key"></i>
                            <span class="submenu-link-text">验证码日志</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- 数据统计 -->
            <li class="nav-item has-submenu <?php echo $current_page === 'dashboard.php' ? 'active open' : ''; ?>">
                <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                    <i class="fas fa-chart-line"></i>
                    <span class="nav-link-text">数据统计</span>
                    <i class="fas fa-chevron-right submenu-arrow"></i>
                </a>
                <ul class="submenu <?php echo $current_page === 'dashboard.php' ? 'show' : ''; ?>">
                    <li class="submenu-item <?php echo $current_page === 'dashboard.php' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/dashboard.php" class="submenu-link">
                            <i class="fas fa-chart-pie"></i>
                            <span class="submenu-link-text">数据仪表盘</span>
                        </a>
                    </li>
                </ul>
            </li>



            <!-- 优惠券管理 -->
            <li class="nav-item has-submenu <?php echo $current_dir === 'coupon_management' ? 'active open' : ''; ?>">
                <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                    <i class="fas fa-ticket-alt"></i>
                    <span class="nav-link-text">优惠券管理</span>
                    <i class="fas fa-chevron-right submenu-arrow"></i>
                </a>
                <ul class="submenu <?php echo $current_dir === 'coupon_management' ? 'show' : ''; ?>">
                    <li class="submenu-item <?php echo $current_page === 'camping_coupons.php' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/coupon_management/camping_coupons.php" class="submenu-link">
                            <i class="fas fa-campground"></i>
                            <span class="submenu-link-text">露营优惠券</span>
                        </a>
                    </li>
                    <li class="submenu-item <?php echo $current_page === 'statistics.php' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/coupon_management/statistics.php" class="submenu-link">
                            <i class="fas fa-analytics"></i>
                            <span class="submenu-link-text">统计分析</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- 权限管理 -->
            <li class="nav-item has-submenu">
                <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                    <i class="fas fa-shield-alt"></i>
                    <span class="nav-link-text">权限管理</span>
                    <i class="fas fa-chevron-right submenu-arrow"></i>
                </a>
                <ul class="submenu">
                    <li class="submenu-item">
                        <a href="/houtai_backup/permission/index.php" class="submenu-link">
                            <i class="fas fa-hand-paper"></i>
                            <span class="submenu-link-text">权限申请</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="/houtai_backup/permission/approval.php" class="submenu-link">
                            <i class="fas fa-check-double"></i>
                            <span class="submenu-link-text">权限审批</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="/houtai_backup/permission/roles.php" class="submenu-link">
                            <i class="fas fa-users-cog"></i>
                            <span class="submenu-link-text">角色管理</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="/houtai_backup/permission/config.php" class="submenu-link">
                            <i class="fas fa-key"></i>
                            <span class="submenu-link-text">权限配置</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- 部门管理 -->
            <li class="nav-item has-submenu">
                <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                    <i class="fas fa-building"></i>
                    <span>部门管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </a>
                <ul class="submenu">
                    <li class="submenu-item">
                        <a href="/houtai_backup/department/index.php" class="submenu-link">
                            <i class="fas fa-sitemap"></i>
                            <span>部门总览</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="/houtai_backup/department/my_department.php" class="submenu-link">
                            <i class="fas fa-users"></i>
                            <span>我的部门</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="#" class="submenu-link" onclick="showComingSoon('员工管理')">
                            <i class="fas fa-user-tie"></i>
                            <span>员工管理</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="#" class="submenu-link" onclick="showComingSoon('组织架构')">
                            <i class="fas fa-project-diagram"></i>
                            <span>组织架构</span>
                        </a>
                    </li>
                </ul>
            </li>



            <!-- 系统设置 -->
            <li class="nav-item has-submenu">
                <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </a>
                <ul class="submenu">
                    <li class="submenu-item">
                        <a href="#" class="submenu-link" onclick="showComingSoon('系统配置')">
                            <i class="fas fa-sliders-h"></i>
                            <span>系统配置</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="#" class="submenu-link" onclick="showComingSoon('日志管理')">
                            <i class="fas fa-file-alt"></i>
                            <span>日志管理</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="#" class="submenu-link" onclick="showComingSoon('系统监控')">
                            <i class="fas fa-chart-line"></i>
                            <span>系统监控</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="#" class="submenu-link" onclick="showComingSoon('备份恢复')">
                            <i class="fas fa-database"></i>
                            <span>备份恢复</span>
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </nav>


</aside>

<script>
// 菜单交互脚本
function toggleSubmenu(element) {
    const submenu = element.nextElementSibling;
    const arrow = element.querySelector('.submenu-arrow');
    const navItem = element.parentElement;

    // 切换显示状态
    submenu.classList.toggle('show');
    navItem.classList.toggle('expanded');

    // 旋转箭头
    if (submenu.classList.contains('show')) {
        arrow.style.transform = 'rotate(180deg)';
    } else {
        arrow.style.transform = 'rotate(0deg)';
    }
}

function showComingSoon(feature) {
    alert(`${feature}功能正在开发中，敬请期待！`);
}

// 页面加载时展开当前活动的菜单
document.addEventListener('DOMContentLoaded', function() {
    const activeNavItem = document.querySelector('.nav-item.active.has-submenu');
    if (activeNavItem) {
        const submenu = activeNavItem.querySelector('.submenu');
        const arrow = activeNavItem.querySelector('.submenu-arrow');
        if (submenu && !submenu.classList.contains('show')) {
            submenu.classList.add('show');
            activeNavItem.classList.add('expanded');
            if (arrow) {
                arrow.style.transform = 'rotate(180deg)';
            }
        }
    }
});
</script>
