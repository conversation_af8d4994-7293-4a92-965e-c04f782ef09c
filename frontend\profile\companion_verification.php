<?php
session_start();

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit();
}

// 数据库连接
require_once '../db_config.php';

$user_id = $_SESSION['user_id'];

// 获取数据库连接
try {
    $pdo = getDbConnection();
} catch (Exception $e) {
    die('数据库连接失败');
}

// 获取用户信息
$stmt = $pdo->prepare("SELECT username, avatar FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: ../login/index.php');
    exit();
}

$user_username = $user['username'];
$user_avatar = $user['avatar'] ?: 'https://s1.imagehub.cc/images/2025/05/02/default-avatar.png';

// 获取陪玩认证状态
$companion_verification = null;
$is_verified = false;

try {
    $stmt = $pdo->prepare("SELECT * FROM companion_verification WHERE user_id = ? ORDER BY submitted_at DESC LIMIT 1");
    $stmt->execute([$user_id]);
    $companion_verification = $stmt->fetch();

    $is_verified = !empty($companion_verification) && $companion_verification['verification_status'] === 'approved';
} catch (PDOException $e) {
    // 表不存在，默认为未认证状态
    $companion_verification = null;
    $is_verified = false;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>陪玩认证 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #6F7BF5 0%, #40E0D0 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid #6F7BF5;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-details h2 {
            font-size: 18px;
            color: #333;
            margin-bottom: 5px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-verified {
            background: rgba(78, 205, 196, 0.1);
            color: #4ECDC4;
            border: 1px solid rgba(78, 205, 196, 0.2);
        }

        .status-unverified {
            background: rgba(255, 138, 101, 0.1);
            color: #FF8A65;
            border: 1px solid rgba(255, 138, 101, 0.2);
        }

        .info-section {
            margin: 20px 0;
        }

        .info-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 12px;
            margin-bottom: 10px;
        }

        .info-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #6F7BF5 0%, #40E0D0 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 12px;
            font-size: 16px;
        }

        .info-content {
            flex: 1;
        }

        .info-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }

        .info-desc {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .action-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #6F7BF5 0%, #40E0D0 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 20px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(111, 123, 245, 0.3);
        }

        .action-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .verification-info {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            margin: 15px 0;
        }

        .verification-info h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .verification-info p {
            color: #666;
            font-size: 13px;
            line-height: 1.5;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="history.back()">
        <i class="fas fa-arrow-left"></i>
    </button>

    <div class="container">
        <div class="header">
            <h1 style="color: #333; margin-bottom: 10px;">陪玩认证</h1>
            <p style="color: #666; font-size: 14px;">成为认证陪玩，开启收益之旅</p>
        </div>

        <div class="user-info">
            <div class="user-avatar">
                <img src="<?php echo htmlspecialchars($user_avatar); ?>" alt="用户头像">
            </div>
            <div class="user-details">
                <h2><?php echo htmlspecialchars($user_username); ?></h2>
                <?php if ($is_verified): ?>
                    <span class="status-badge status-verified">已认证陪玩</span>
                <?php else: ?>
                    <span class="status-badge status-unverified">未认证</span>
                <?php endif; ?>
            </div>
        </div>

        <?php if ($companion_verification): ?>
        <div class="verification-info">
            <h4>认证状态</h4>
            <p><strong>状态：</strong>
                <?php
                switch($companion_verification['verification_status']) {
                    case 'pending': echo '审核中'; break;
                    case 'approved': echo '已通过'; break;
                    case 'rejected': echo '已拒绝'; break;
                    default: echo '未知状态';
                }
                ?>
            </p>
            <p><strong>提交时间：</strong><?php echo date('Y-m-d H:i', strtotime($companion_verification['submitted_at'])); ?></p>
            <?php if ($companion_verification['verification_status'] === 'rejected' && $companion_verification['reject_reason']): ?>
                <p><strong>拒绝原因：</strong><?php echo htmlspecialchars($companion_verification['reject_reason']); ?></p>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div class="info-section">
            <div class="info-title">陪玩认证权益</div>

            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">专属标识</div>
                    <div class="info-desc">获得认证陪玩专属标识</div>
                </div>
            </div>

            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">收益机会</div>
                    <div class="info-desc">通过陪玩服务获得收益</div>
                </div>
            </div>

            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">优先推荐</div>
                    <div class="info-desc">在陪玩列表中优先展示</div>
                </div>
            </div>
        </div>

        <?php if (!$is_verified): ?>
            <?php if (!$companion_verification || $companion_verification['verification_status'] === 'rejected'): ?>
                <button class="action-btn" onclick="showToast('陪玩认证功能即将上线')">
                    <i class="fas fa-user-plus"></i>
                    申请陪玩认证
                </button>
            <?php elseif ($companion_verification['verification_status'] === 'pending'): ?>
                <button class="action-btn" disabled>
                    <i class="fas fa-clock"></i>
                    审核中，请耐心等待
                </button>
            <?php endif; ?>
        <?php else: ?>
            <button class="action-btn" onclick="showToast('您已是认证陪玩')">
                <i class="fas fa-check-circle"></i>
                已认证陪玩
            </button>
        <?php endif; ?>
    </div>

    <script>
        function showToast(message) {
            const toast = document.createElement('div');
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 10000;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '1';
            }, 10);

            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }
    </script>
</body>
</html>
