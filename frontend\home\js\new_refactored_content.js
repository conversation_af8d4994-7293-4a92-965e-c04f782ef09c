/**
 * 全新重构的内容管理系统
 * 处理金刚功能区下方的所有内容展示和交互
 */

// 内容数据模板
const contentTemplates = {
    city: {
        title: '城市玩伴',
        icon: 'fas fa-building',
        subOptions: ['娱乐', '旅游', '社交', '运动', '其他'],
        content: [
            {
                title: '周末咖啡探店',
                description: '发现城市角落的精品咖啡馆，一起品尝手冲咖啡',
                image: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg',
                tags: ['美食', '社交', '周末'],
                action: '立即报名'
            },
            {
                title: '城市夜跑团',
                description: '每周三次夜跑活动，沿着城市最美的路线',
                image: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg',
                tags: ['运动', '健身', '夜跑'],
                action: '加入跑团'
            },
            {
                title: '密室逃脱挑战',
                description: '最新主题密室等你来挑战！团队协作，智慧碰撞',
                image: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg',
                tags: ['娱乐', '团队', '挑战'],
                action: '立即预约'
            }
        ]
    },
    game: {
        title: '游戏玩伴',
        icon: 'fas fa-gamepad',
        subOptions: ['MOBA', 'FPS', 'MMORPG', '手游', '其他'],
        content: [
            {
                title: '王者荣耀陪玩',
                description: '专业陪玩，声音甜美，技术过硬，带你轻松上分',
                image: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg',
                tags: ['王者荣耀', '上分', '陪玩'],
                action: '立即邀请'
            },
            {
                title: 'LOL峡谷钢琴家',
                description: '多年电竞经验，擅长中单和打野位置',
                image: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg',
                tags: ['英雄联盟', '中单', '打野'],
                action: '立即邀请'
            },
            {
                title: 'CSGO老兵',
                description: 'FPS游戏专家，枪法精准，战术意识强',
                image: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg',
                tags: ['CSGO', 'FPS', '枪法'],
                action: '立即邀请'
            }
        ]
    },
    group: {
        title: '组局搭子',
        icon: 'fas fa-users',
        subOptions: ['娱乐', '钓鱼', '社交', '运动', '户外'],
        content: [
            {
                title: '周末羽毛球局',
                description: '寻找球友一起打羽毛球，场地已订好，水平不限',
                image: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg',
                tags: ['羽毛球', '运动', '周末'],
                action: '立即加入'
            },
            {
                title: '科幻大片观影团',
                description: '最新上映的科幻巨制，一起去电影院感受震撼',
                image: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg',
                tags: ['电影', '科幻', '观影'],
                action: '加入观影'
            },
            {
                title: '周末读书分享会',
                description: '每周末的读书分享会，一起阅读、讨论、分享',
                image: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg',
                tags: ['读书', '分享', '成长'],
                action: '加入读书会'
            }
        ]
    },
    ticket: {
        title: '景点门票',
        icon: 'fas fa-ticket-alt',
        subOptions: ['游乐园', '景区', '公园', '其他'],
        content: [
            {
                title: '奇幻乐园一日通票',
                description: '畅玩所有项目，包含过山车、旋转木马、摩天轮',
                image: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg',
                tags: ['游乐园', '通票', '亲子'],
                action: '立即抢购'
            },
            {
                title: '蓝色海洋世界',
                description: '探索神秘海底世界，观赏海豚表演',
                image: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg',
                tags: ['海洋馆', '海豚', '科普'],
                action: '立即预订'
            },
            {
                title: '国家森林公园',
                description: '拥抱大自然，徒步登山，观赏瀑布',
                image: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg',
                tags: ['自然', '登山', '瀑布'],
                action: '预订门票'
            }
        ]
    }
};

// 当前激活的功能区
let currentFeature = 'city';

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeNewRefactoredContent();
    setupEventListeners();
});

/**
 * 初始化新的重构内容系统
 */
function initializeNewRefactoredContent() {
    console.log('初始化新的重构内容系统');

    // 等待原有系统初始化完成
    setTimeout(() => {
        // 监听金刚功能区的变化
        observeFeatureChanges();

        // 初始化显示城市玩伴内容
        displayContent('city');
    }, 200);
}

/**
 * 监听金刚功能区的变化
 */
function observeFeatureChanges() {
    const featureItems = document.querySelectorAll('.feature-item');

    featureItems.forEach(item => {
        item.addEventListener('click', function() {
            const feature = this.dataset.feature;
            console.log('点击功能区:', feature);
            if (feature && contentTemplates[feature]) {
                currentFeature = feature;
                displayContent(feature);
            }
        });
    });

    // 监听子选项的点击
    const subOptionTabs = document.querySelectorAll('.sub-option-tab');
    subOptionTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 更新子选项的激活状态
            const parentList = this.closest('.sub-options-list');
            if (parentList) {
                parentList.querySelectorAll('.sub-option-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            }

            // 根据子选项筛选内容
            const subOption = this.dataset.subOption;
            console.log('点击子选项:', subOption);
            filterContentBySubOption(subOption);
        });
    });
}

/**
 * 显示对应功能区的内容
 */
function displayContent(feature) {
    const template = contentTemplates[feature];
    if (!template) return;

    console.log('显示内容:', feature);

    // 更新子选项
    updateSubOptions(template.subOptions, feature);

    // 更新主内容区域
    updateMainContent(template);
}

/**
 * 更新子选项
 */
function updateSubOptions(subOptions, feature) {
    // 隐藏所有子选项列表
    const allSubLists = document.querySelectorAll('.sub-options-list');
    allSubLists.forEach(list => list.classList.remove('active'));

    // 显示对应的子选项列表
    const targetSubList = document.getElementById(`sub-options-${feature}`);
    if (targetSubList) {
        targetSubList.classList.add('active');
    }
}

/**
 * 更新主内容区域
 */
function updateMainContent(template) {
    const contentDisplay = document.getElementById('dynamic-content-display');
    if (!contentDisplay) return;

    const html = `
        <div class="content-section active">
            <div class="section-title">
                <h2>
                    <i class="${template.icon}"></i>
                    ${template.title}
                </h2>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">全部</button>
                    <button class="filter-btn" data-filter="hot">热门</button>
                    <button class="filter-btn" data-filter="new">最新</button>
                </div>
            </div>

            <div class="content-grid">
                ${template.content.map(item => createContentCard(item)).join('')}
            </div>

            <button class="load-more-btn">查看更多${template.title}</button>
        </div>
    `;

    contentDisplay.innerHTML = html;

    // 绑定事件
    bindContentEvents();
}

/**
 * 创建内容卡片
 */
function createContentCard(item) {
    return `
        <div class="content-card">
            <img src="${item.image}" alt="${item.title}" class="card-image">
            <div class="card-content">
                <h3 class="card-title">${item.title}</h3>
                <p class="card-description">${item.description}</p>
                <div class="card-tags">
                    ${item.tags.map(tag => `<span class="card-tag">${tag}</span>`).join('')}
                </div>
                <button class="card-action">${item.action}</button>
            </div>
        </div>
    `;
}

/**
 * 绑定内容事件
 */
function bindContentEvents() {
    // 筛选按钮事件
    const filterBtns = document.querySelectorAll('.filter-btn');
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // 更新按钮状态
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // 执行筛选逻辑
            const filter = this.dataset.filter;
            filterContent(filter);
        });
    });

    // 卡片操作按钮事件
    const actionBtns = document.querySelectorAll('.card-action');
    actionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const card = this.closest('.content-card');
            const title = card.querySelector('.card-title').textContent;
            showToast(`正在处理 ${title} 的请求...`);
        });
    });

    // 加载更多按钮事件
    const loadMoreBtn = document.querySelector('.load-more-btn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            showToast('更多内容正在开发中，敬请期待！');
        });
    }
}

/**
 * 筛选内容
 */
function filterContent(filter) {
    const cards = document.querySelectorAll('.content-card');

    cards.forEach(card => {
        if (filter === 'all') {
            card.style.display = 'block';
        } else {
            // 简单的筛选逻辑，实际项目中可以根据具体需求实现
            card.style.display = Math.random() > 0.3 ? 'block' : 'none';
        }
    });

    // 检查是否有可见卡片
    const visibleCards = document.querySelectorAll('.content-card[style*="block"]');
    if (visibleCards.length === 0) {
        showEmptyState();
    } else {
        hideEmptyState();
    }
}

/**
 * 显示空状态
 */
function showEmptyState() {
    const contentGrid = document.querySelector('.content-grid');
    if (contentGrid && !document.querySelector('.empty-state')) {
        const emptyState = document.createElement('div');
        emptyState.className = 'empty-state';
        emptyState.innerHTML = `
            <i class="fas fa-box-open"></i>
            <p>暂无符合条件的内容，试试其他筛选条件吧！</p>
        `;
        contentGrid.after(emptyState);
    }
}

/**
 * 隐藏空状态
 */
function hideEmptyState() {
    const emptyState = document.querySelector('.empty-state');
    if (emptyState) {
        emptyState.remove();
    }
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 位置选择器点击事件
    const locationSelector = document.getElementById('current-location-display');
    if (locationSelector) {
        locationSelector.addEventListener('click', function() {
            // 触发城市选择器
            const citySelector = document.getElementById('city-selector');
            const overlay = document.getElementById('overlay');
            if (citySelector && overlay) {
                citySelector.style.display = 'block';
                overlay.style.display = 'block';
            }
        });
    }

    // 日期选择器点击事件
    const dateSelector = document.getElementById('current-date-display');
    if (dateSelector) {
        dateSelector.addEventListener('click', function() {
            // 触发日期选择器
            const dateModal = document.getElementById('date-selector-modal');
            const overlay = document.getElementById('overlay');
            if (dateModal && overlay) {
                dateModal.style.display = 'block';
                overlay.style.display = 'block';
            }
        });
    }

    // 探索按钮点击事件
    const exploreBtn = document.getElementById('main-explore-btn');
    if (exploreBtn) {
        exploreBtn.addEventListener('click', function() {
            showToast('开始为您搜索精彩内容...');
        });
    }
}

/**
 * 根据子选项筛选内容
 */
function filterContentBySubOption(subOption) {
    console.log('筛选子选项内容:', subOption);

    // 这里可以根据子选项重新加载内容
    // 目前先显示一个提示
    showToast(`正在加载 ${subOption} 相关内容...`);

    // 可以在这里调用API或者筛选现有内容
    // 例如：loadContentBySubOption(currentFeature, subOption);
}

/**
 * 显示Toast提示
 */
function showToast(message) {
    // 使用现有的showToast函数或创建新的
    if (typeof window.showToast === 'function') {
        window.showToast(message);
    } else {
        console.log('Toast:', message);
        // 简单的toast实现
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            font-size: 14px;
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 2000);
    }
}
