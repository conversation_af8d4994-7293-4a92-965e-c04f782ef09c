# 趣玩星球项目

## 系统架构
- **前端技术栈**：HTML, CSS, JavaScript, PHP
- **后端技术栈**：PHP
- **数据库**：MySQL
- **图片存储**：Cloudinary
- **地图服务**：高德地图API

## 项目结构
- `frontend/`: 前台文件目录
  - `login/`: 登录页面相关文件
    - `js/`: 登录页面JavaScript文件
    - `css/`: 登录页面CSS文件
  - 其他页面按照相同结构组织
- `sql/`: 数据库相关文件
- `index.php`: 首页

## 数据库配置
- **数据库名**：quwanplanet
- **数据库用户**：quwanplanet
- **数据库密码**：nJmJm23FB4Xn6Fc3
- **数据库主机**：localhost
- **数据库端口**：3306
- **字符集**：utf8mb4
- **排序规则**：utf8mb4_general_ci
- **重要说明**：请勿修改数据库名、用户、密码等信息，否则可能导致系统无法正常运行。
以及涉及到新增数据库字段或者表结构，请务必把相关代码给管理员，管理员需要到宝塔面板的phpMyAdmin中执行相关SQL语句。

## 域名信息
- **前台域名**：planet.vancrest.xyz
- **后台域名**：vansmrz.vancrest.xyz

## 文件夹信息

# Vue客服系统实施方案

## 项目架构
- **主项目**：继续使用PHP语言
- **客服系统**：使用Vue语言实现前后台即时通讯
- **部署方式**：Vue构建后的静态文件直接上传到宝塔

## Vue客服系统文件结构
```
frontend/
└── customer_service_vue/          # 前台客服系统（用户端）
    ├── index.html                 # 用户聊天页面
    ├── css/
    ├── js/
    └── assets/

houtai_backup/
└── customer_service_vue/          # 后台客服系统（客服管理端）
    ├── index.html                 # 客服工作台
    ├── css/
    ├── js/
    └── assets/

frontend/api/customer_service/     # PHP API接口
├── sse_chat.php                   # SSE推送接口
├── send_message.php               # 发送消息接口
├── upload_image.php               # 图片上传接口
└── session_management.php         # 会话管理接口
```

## 访问路径
- 用户端：https://planet.vancrest.xyz/frontend/customer_service_vue/
- 管理端：https://vansmrz.vancrest.xyz/houtai_backup/customer_service_vue/

# 趣玩星球 - Vue + uniCloud 重构版

## 项目概述
基于Vue 3 + uniCloud的全栈社交平台，支持H5、小程序、APP多端部署。

## 技术栈
- **前端**: Vue 3 + Vite + uniapp
- **后端**: uniCloud云函数 + 云数据库
- **实时通信**: uniCloud实时数据推送
- **UI框架**: uni-ui + 自定义组件
- **状态管理**: Pinia
- **路由**: uni-router

## 项目结构
```
quwanplanet-vue/
├── src/
│   ├── pages/           # 页面文件
│   │   ├── index/       # 首页
│   │   ├── login/       # 登录注册
│   │   ├── profile/     # 个人中心
│   │   ├── customer-service/ # 客服系统
│   │   └── admin/       # 管理后台
│   ├── components/      # 组件
│   ├── store/          # 状态管理
│   ├── utils/          # 工具函数
│   └── static/         # 静态资源
├── uniCloud-aliyun/    # 云函数
│   ├── cloudfunctions/ # 云函数
│   └── database/       # 数据库初始化
├── manifest.json       # 应用配置
├── pages.json         # 页面配置
└── uni.scss          # 全局样式
```

## 核心功能模块

### 1. 用户系统
- 手机号一键注册登录
- 验证码登录（6位数字输入框）
- 完善个人信息（头像、昵称、地区等）
- 3天免登录
- 严格的昵称验证规则

### 2. 客服系统
- 实时聊天功能
- AI机器人自动回复
- 人工客服接入
- 表情包和图片发送
- 服务评价系统

### 3. 管理后台
- 员工ID登录系统
- 验证码锁定机制
- 权限分级管理
- 用户数据管理
- 客服工作台

### 4. 实时通信
- uniCloud实时数据推送
- 消息即时送达
- 在线状态显示
- 消息已读状态

## 设计规范

### 主题色彩
- 主色调: #6F7BF5
- 辅助色: #AFFBF2 (薄荷绿)
- 强调色: #40E0D0 (青绿色)
- 警告色: #FF6B6B (西瓜红)
- 成功色: #4ECDC4

### 组件规范
- 四角星加载动画
- 纯CSS图标优先
- 白色状态栏
- 仅支持垂直滚动
- 统一的弹窗样式

## 数据库设计

### 用户表 (users)
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  phone VARCHAR(11) UNIQUE NOT NULL,
  nickname VARCHAR(20),
  avatar TEXT,
  gender TINYINT,
  birthday DATE,
  region VARCHAR(100),
  email VARCHAR(100),
  password VARCHAR(255),
  bio TEXT,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 客服会话表 (customer_service_sessions)
```sql
CREATE TABLE customer_service_sessions (
  id VARCHAR(50) PRIMARY KEY,
  user_id INT NOT NULL,
  customer_service_id INT,
  status ENUM('waiting', 'active', 'closed') DEFAULT 'waiting',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 消息表 (messages)
```sql
CREATE TABLE messages (
  id INT PRIMARY KEY AUTO_INCREMENT,
  session_id VARCHAR(50) NOT NULL,
  sender_id INT,
  sender_type ENUM('user', 'customer_service', 'bot') NOT NULL,
  content TEXT NOT NULL,
  message_type ENUM('text', 'image', 'emoji') DEFAULT 'text',
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES customer_service_sessions(id)
);
```

### 管理员表 (admins)
```sql
CREATE TABLE admins (
  id INT PRIMARY KEY AUTO_INCREMENT,
  employee_id VARCHAR(20) UNIQUE NOT NULL,
  name VARCHAR(50) NOT NULL,
  department VARCHAR(50),
  role ENUM('super_admin', 'admin', 'customer_service') NOT NULL,
  permissions JSON,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 部署配置

### 域名配置
- 前端H5: https://planet.vancrest.xyz
- 管理后台: https://vansmrz.vancrest.xyz/admin
- API接口: uniCloud自动生成

### uniCloud配置
- 服务商: 阿里云
- 地域: 上海
- 数据库: MongoDB
- 存储: 云存储

## 开发计划

### 第一阶段 - 基础框架 (1-2天)
- [x] 项目初始化
- [ ] 基础组件开发
- [ ] 路由配置
- [ ] 状态管理设置

### 第二阶段 - 用户系统 (2-3天)
- [ ] 登录注册页面
- [ ] 验证码功能
- [ ] 个人信息完善
- [ ] 用户状态管理

### 第三阶段 - 客服系统 (3-4天)
- [ ] 聊天界面
- [ ] 实时消息推送
- [ ] AI机器人
- [ ] 文件上传功能

### 第四阶段 - 管理后台 (2-3天)
- [ ] 管理员登录
- [ ] 用户管理
- [ ] 客服工作台
- [ ] 权限控制

### 第五阶段 - 优化部署 (1-2天)
- [ ] 性能优化
- [ ] 安全加固
- [ ] 域名部署
- [ ] 测试验收

## 注意事项
1. 所有敏感信息使用环境变量
2. 严格的输入验证和XSS防护
3. 实时功能使用uniCloud推送，确保稳定性
4. 移动端适配优先，响应式设计
5. 代码注释完整，便于维护

### 客服管理系统
- **客服系统文件夹**: `houtai_backup/customer_service_system/` - 独立的客服管理后台系统
- **客服登录**: 通过主登录页面的客服登录选项卡进入独立的客服管理后台
- **客服账号**: 独立的客服账号系统，与主后台admin_users分离
- **权限管理**: 基于角色的权限控制，客服和超级管理员有不同的功能访问权限


## 图片存储 (Cloudinary)
- **Cloud name**: dwcauq0wy
- **API KEY**: 965165511998959
- **API Secret**: JYnkxTIAAC3GLuf3u6iiQpfqfMA
- **预设名称**: chat_app_preset
- **使用说明**: 上传图片后，Cloudinary会返回URL，需将此URL保存到数据库中（如用户头像等）

## 定位系统 (高德地图API)
- **Web端Key**: e318a539d606974c5f13654e905cf555
- **安全密钥**: b2d738d21b1aed6f1ec0d61a9c935a7b
- **定位要求**: 定位到"市"即可，无需定位到省或者区

## 页面要求
- 所有页面为手机H5网页端
- 网页需适配各种屏幕尺寸的手机
- 页面只能上下滑动，不能左右滑动，不能放大或缩小页面
- 弹窗使用toast，禁用浏览器的原生提示

## 设计规范
- **主题色**: #6F7BF5
- **辅助色**:
  - #FF6B6B（珊瑚红）
  - #4D5DFB（靛蓝色）
  - #FFD166（明亮黄）
  - #06D6A0（薄荷绿）
- **文字颜色**:
  - 主要文字: #333333
  - 次要文字: #666666
  - 提示文字: #999999
- **背景色**: #F8F9FA
