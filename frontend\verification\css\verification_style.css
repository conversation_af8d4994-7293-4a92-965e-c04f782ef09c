/**
 * 实名认证页面样式
 * 简洁、现代化设计
 */

/* ===== CSS变量定义 ===== */
:root {
    /* 主题色系 */
    --primary-color: #6F7BF5;
    --secondary-color: #AFFBF2;
    --accent-color: #40E0D0;
    --success-color: #4ECDC4;
    --warning-color: #FF8A65;
    --error-color: #FF5722;
    --verified-color: #00C851;
    --pending-color: #FF8800;
    --rejected-color: #FF4444;
    
    /* 背景和文字 */
    --bg-primary: #F8F9FA;
    --bg-secondary: #FFFFFF;
    --bg-card: #FFFFFF;
    --text-primary: #2C3E50;
    --text-secondary: #7F8C8D;
    --text-light: #BDC3C7;
    --border-color: #E9ECEF;
    
    /* 阴影和圆角 */
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-strong: 0 8px 32px rgba(0, 0, 0, 0.16);
    --border-radius: 12px;
    --border-radius-large: 16px;
    --border-radius-xl: 20px;
    
    /* 动画 */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease;
}

/* ===== 全局样式 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    padding-bottom: 80px;
}

/* ===== 顶部导航 ===== */
.verification-header {
    position: sticky;
    top: 0;
    z-index: 100;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    max-width: 100%;
}

.back-btn, .help-btn {
    width: 40px;
    height: 40px;
    background: var(--bg-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: var(--transition-fast);
    border: 1px solid var(--border-color);
    text-decoration: none;
}

.back-btn:hover, .help-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.header-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
    flex: 1;
}

.header-actions {
    width: 40px;
    display: flex;
    justify-content: flex-end;
}

/* ===== 认证状态卡片 ===== */
.verification-status-card {
    margin: 20px 16px;
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
}

.status-content {
    padding: 24px;
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 16px;
    border: 2px solid var(--success-color);
    box-shadow: var(--shadow-light);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    flex: 1;
}

.username {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.verification-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
}

.verification-badge.verified {
    background: rgba(78, 205, 196, 0.1);
    color: var(--verified-color);
    border: 1px solid rgba(78, 205, 196, 0.2);
}

.verification-badge.unverified {
    background: rgba(255, 138, 101, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 138, 101, 0.2);
}

.verification-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.verification-status, .verification-time {
    text-align: center;
    padding: 12px;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
}

.verification-status .label, .verification-time .label {
    display: block;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.verification-status .value, .verification-time .value {
    font-size: 14px;
    font-weight: 600;
}

.status-pending { color: var(--pending-color); }
.status-approved { color: var(--verified-color); }
.status-rejected { color: var(--rejected-color); }

/* ===== 区块标题 ===== */
.section-header {
    margin: 32px 16px 16px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

/* ===== 认证说明 ===== */
.info-list {
    margin: 0 16px;
}

.info-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    margin-bottom: 12px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.info-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.info-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--success-color) 0%, var(--accent-color) 100%);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: white;
    font-size: 20px;
}

.info-content {
    flex: 1;
}

.info-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.info-desc {
    font-size: 14px;
    color: var(--text-secondary);
}

/* ===== 认证操作 ===== */
.verification-actions {
    margin: 32px 16px;
    text-align: center;
}

.verify-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 16px 32px;
    border-radius: var(--border-radius-large);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    border: none;
    text-decoration: none;
}

.verify-btn.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    box-shadow: var(--shadow-medium);
}

.verify-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-strong);
}

.verify-btn.disabled {
    background: var(--bg-primary);
    color: var(--text-secondary);
    cursor: not-allowed;
    border: 1px solid var(--border-color);
}

.verified-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px;
    background: rgba(78, 205, 196, 0.1);
    border-radius: var(--border-radius-large);
    color: var(--verified-color);
    font-weight: 600;
    border: 1px solid rgba(78, 205, 196, 0.2);
}

/* ===== 认证须知 ===== */
.notice-content {
    margin: 0 16px;
    background: var(--bg-card);
    border-radius: var(--border-radius-large);
    padding: 20px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.notice-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.notice-item:last-child {
    border-bottom: none;
}

.notice-number {
    width: 24px;
    height: 24px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
}

.notice-text {
    font-size: 14px;
    color: var(--text-secondary);
}

/* ===== 认证表单弹窗 ===== */
.verification-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.verification-modal.show {
    opacity: 1;
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--border-radius-large);
    max-width: 90%;
    width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-strong);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.verification-modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.close-btn {
    width: 32px;
    height: 32px;
    background: var(--bg-primary);
    border: none;
    border-radius: 50%;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: var(--error-color);
    color: white;
}

.modal-body {
    padding: 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition-fast);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(111, 123, 245, 0.1);
}

.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition-fast);
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(111, 123, 245, 0.05);
}

.upload-area i {
    font-size: 32px;
    color: var(--text-light);
    margin-bottom: 8px;
    display: block;
}

.upload-area span {
    font-size: 14px;
    color: var(--text-secondary);
}

.submit-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* ===== 响应式设计 ===== */
@media (max-width: 480px) {
    .header-content {
        padding: 12px 16px;
    }
    
    .status-content {
        padding: 20px;
    }
    
    .user-avatar {
        width: 50px;
        height: 50px;
    }
    
    .username {
        font-size: 16px;
    }
    
    .verification-info {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .modal-header, .modal-body {
        padding: 16px 20px;
    }
}
