<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit;
}

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// 初始化用户信息变量
$user_username = '用户';
$user_quwan_id = 'N/A';
$user_bio = '这个人很懒，还没有填写个人简介';
$user_display_avatar_url = 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'; // 默认头像
$user_nameplate_url = null; // 铭牌URL
$user_nameplate_active = false; // 铭牌是否启用

try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 查询需要的字段，包括铭牌信息和实名认证状态
    $stmt = $pdo->prepare("SELECT username, quwan_id, bio, avatar, nameplate_url, nameplate_active, is_verified FROM users WHERE id = :user_id");
    $stmt->execute(['user_id' => $_SESSION['user_id']]);
    $db_user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$db_user) {
        session_destroy();
        header('Location: ../login/index.php');
        exit;
    } else {
        $user_username = $db_user['username'];
        $user_quwan_id = $db_user['quwan_id'];
        if (!empty($db_user['bio'])) {
            $user_bio = $db_user['bio'];
        }
        if (!empty($db_user['avatar'])) {
            $user_display_avatar_url = $db_user['avatar']; // 使用数据库中的头像
        }
        if (!empty($db_user['nameplate_url']) && $db_user['nameplate_active'] == 1) {
            $user_nameplate_url = $db_user['nameplate_url']; // 使用数据库中的铭牌
            $user_nameplate_active = true;
        }

        // 获取实名认证状态 - 检查realname_verification表
        $user_is_verified = false;
        try {
            // 首先尝试查询realname_verification表
            $stmt = $pdo->prepare("SELECT verification_status FROM realname_verification WHERE user_id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $verification_result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($verification_result) {
                $user_is_verified = ($verification_result['verification_status'] === 'approved');
            } else {
                // 如果realname_verification表中没有记录，检查users表的is_verified字段
                $user_is_verified = !empty($db_user['is_verified']) && $db_user['is_verified'] == 1;
            }
        } catch (PDOException $e) {
            // 如果realname_verification表不存在，检查users表的is_verified字段
            $user_is_verified = !empty($db_user['is_verified']) && $db_user['is_verified'] == 1;
        }

        // 获取会员状态（简单检查，实际应该查询members表）
        $user_is_member = false; // 默认未开通会员
    }
} catch (PDOException $e) {
    error_log("个人页面错误: " . $e->getMessage());
    // $db_error = true; // 即使出错，也使用默认值显示页面
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#FFFFFF">
    <title>我的 - 趣玩星球</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/modern_style.css">
    <?php
    include '../includes/bottom_nav.php';
    echo renderBottomNavCSS();
    ?>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 实时通知系统 -->
    <?php include '../includes/realtime_notifications.php'; ?>
</head>
<body>
    <!-- 现代化Toast提示 -->
    <div class="modern-toast" id="toast"></div>

    <!-- 现代化用户信息卡片 -->
    <div class="modern-user-card">
        <!-- 渐变背景头部 -->
        <div class="card-header">
            <!-- 顶部操作按钮 -->
            <div class="modern-card-actions">
                <a href="../customer_service/index.php" class="modern-action-btn">
                    <i class="fas fa-headset"></i>
                </a>
                <a href="settings.php" class="modern-action-btn">
                    <i class="fas fa-cog"></i>
                </a>
            </div>

            <!-- 重新设计的用户信息布局 -->
            <div class="modern-user-layout">
                <!-- 头像在左侧 -->
                <div class="modern-avatar-container">
                    <div class="modern-avatar">
                        <img src="<?php echo htmlspecialchars($user_display_avatar_url); ?>" alt="用户头像">
                    </div>
                    <?php if ($user_nameplate_active && $user_nameplate_url): ?>
                    <div class="modern-nameplate">
                        <img src="<?php echo htmlspecialchars($user_nameplate_url); ?>" alt="用户铭牌">
                    </div>
                    <?php endif; ?>
                </div>

                <!-- 用户信息在右侧 -->
                <div class="modern-user-info">
                    <!-- 昵称在上方 -->
                    <h2 class="modern-user-name">
                        <?php echo htmlspecialchars($user_username); ?>
                    </h2>

                    <!-- 趣玩ID在昵称下方 -->
                    <div class="modern-user-id-container">
                        <span class="modern-user-id">趣玩ID: <?php echo htmlspecialchars($user_quwan_id); ?></span>
                        <button class="modern-copy-btn" data-id="<?php echo htmlspecialchars($user_quwan_id); ?>">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>

                    <!-- 会员图标和实名认证图标在趣玩ID下方 -->
                    <div class="modern-status-icons">
                        <span class="member-icon <?php echo $user_is_member ? 'active' : 'inactive'; ?>" title="会员状态">
                            <i class="fas fa-caret-down" style="transform: rotate(180deg);"></i>
                        </span>
                        <a href="verification.php" class="verified-icon <?php echo $user_is_verified ? 'active' : 'inactive'; ?>" title="实名认证 - 状态: <?php echo $user_is_verified ? '已认证' : '未认证'; ?>">
                            <i class="fas fa-id-card"></i>
                        </a>
                    </div>

                </div>
            </div>
        </div>

        <!-- 卡片内容区域 -->
        <div class="card-content">
            <!-- 现代化统计信息 -->
            <div class="modern-stats">
                <div class="stat-item">
                    <div class="stat-value">0</div>
                    <div class="stat-label">关注</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">0</div>
                    <div class="stat-label">粉丝</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">0</div>
                    <div class="stat-label">获赞</div>
                </div>
            </div>

            <!-- 现代化用户简介 -->
            <div class="modern-bio">
                <?php echo htmlspecialchars($user_bio); ?>
            </div>
        </div>
    </div>



    <!-- 现代化功能网格容器 -->
    <div class="modern-feature-container">
        <div class="modern-feature-grid">
            <a href="../wallet/index.php" class="modern-feature-item">
                <div class="modern-feature-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="modern-feature-label">钱包</div>
            </a>
            <a href="../orders/index.php" class="modern-feature-item">
                <div class="modern-feature-icon">
                    <i class="fas fa-receipt"></i>
                </div>
                <div class="modern-feature-label">订单</div>
            </a>
            <a href="../coupons/index.php" class="modern-feature-item">
                <div class="modern-feature-icon">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="modern-feature-label">券包</div>
            </a>
            <a href="../activities/index.php" class="modern-feature-item">
                <div class="modern-feature-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <div class="modern-feature-label">活动</div>
            </a>
        </div>
    </div>

    <!-- 现代化功能菜单 -->
    <div class="modern-menu-section">
        <div class="modern-menu-group">
            <a href="../member/index.php" class="modern-menu-item">
                <div class="modern-menu-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="modern-menu-content">
                    <div class="modern-menu-label">会员中心</div>
                    <div class="modern-menu-desc">专属特权与权益管理</div>
                </div>
                <div class="modern-menu-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="../checkin/index.php" class="modern-menu-item">
                <div class="modern-menu-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="modern-menu-content">
                    <div class="modern-menu-label">每日签到</div>
                    <div class="modern-menu-desc">签到领取积分奖励</div>
                </div>
                <div class="modern-menu-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="../shop/index.php" class="modern-menu-item">
                <div class="modern-menu-icon">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <div class="modern-menu-content">
                    <div class="modern-menu-label">趣玩商城</div>
                    <div class="modern-menu-desc">精选好物等你来购</div>
                </div>
                <div class="modern-menu-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
        </div>

        <div class="modern-menu-group">
            <a href="../guild/index.php" class="modern-menu-item">
                <div class="modern-menu-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="modern-menu-content">
                    <div class="modern-menu-label">我的公会</div>
                    <div class="modern-menu-desc">加入公会结识好友</div>
                </div>
                <div class="modern-menu-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="../universe/my_posts.php" class="modern-menu-item">
                <div class="modern-menu-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <div class="modern-menu-content">
                    <div class="modern-menu-label">我的作品</div>
                    <div class="modern-menu-desc">查看发布的内容</div>
                </div>
                <div class="modern-menu-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="../groups/index.php" class="modern-menu-item">
                <div class="modern-menu-icon">
                    <i class="fas fa-user-friends"></i>
                </div>
                <div class="modern-menu-content">
                    <div class="modern-menu-label">组局约伴</div>
                    <div class="modern-menu-desc">发起或参与活动</div>
                </div>
                <div class="modern-menu-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
        </div>

        <div class="modern-menu-group">
            <a href="../creator/index.php" class="modern-menu-item">
                <div class="modern-menu-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="modern-menu-content">
                    <div class="modern-menu-label">创作者中心</div>
                    <div class="modern-menu-desc">管理创作内容</div>
                </div>
                <div class="modern-menu-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
        </div>
    </div>

    <?php echo renderBottomNav('profile', true); ?>

    <script src="js/script.js"></script>
    <?php echo renderBottomNavJS(true); ?>
</body>
</html>
