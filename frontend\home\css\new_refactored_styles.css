/**
 * 全新重构的样式文件
 * 支持金刚功能区下方的所有内容重构
 */

/* 主题色变量 */
:root {
    --primary-color: #6F7BF5;
    --secondary-color: #AFFBF2;
    --accent-color: #40E0D0;
    --warning-color: #FF6B6B;
    --success-color: #4ECDC4;
    --bright-yellow: #FFD166;
    --background-color: #F8F9FA;
    --text-primary: #2C3E50;
    --text-secondary: #7F8C8D;
    --border-color: #E9ECEF;
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* 新的子选项包装器 */
.new-sub-options-wrapper {
    background: white;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--shadow-light);
    margin-bottom: 16px;
    overflow: hidden;
}

/* 子选项容器 */
.sub-options-container {
    padding: 16px 16px 0 16px;
}

/* 子选项列表 */
.sub-options-list {
    display: none;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
}

.sub-options-list.active {
    display: flex;
}

/* 子选项标签 */
.sub-option-tab {
    padding: 8px 16px;
    background: var(--background-color);
    color: var(--text-secondary);
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid var(--border-color);
    white-space: nowrap;
}

.sub-option-tab:hover {
    background: var(--secondary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.sub-option-tab.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(111, 123, 245, 0.3);
}

/* 快速操作区域 */
.quick-actions-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
    border-top: 1px solid var(--border-color);
}

/* 位置日期显示 */
.location-date-display {
    display: flex;
    gap: 12px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    font-size: 13px;
    color: var(--text-primary);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.info-item:hover {
    background: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

.info-item i {
    color: var(--primary-color);
    font-size: 12px;
}

/* 探索按钮 */
.explore-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 4px 12px rgba(111, 123, 245, 0.3);
}

.explore-btn:hover {
    background: #5A67E8;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(111, 123, 245, 0.4);
}

.explore-btn:active {
    transform: translateY(0);
}

.explore-btn i {
    font-size: 13px;
}

/* 动态内容展示区域 */
.dynamic-content-display {
    padding: 20px 16px;
    background: var(--background-color);
    min-height: 400px;
}

/* 内容板块样式 */
.content-section {
    display: none;
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-light);
}

.content-section.active {
    display: block;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 板块标题 */
.section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid var(--background-color);
}

.section-title h2 {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.section-title i {
    color: var(--primary-color);
    font-size: 18px;
}

/* 筛选按钮组 */
.filter-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 6px 14px;
    background: var(--background-color);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn:hover {
    background: var(--secondary-color);
    color: var(--primary-color);
    border-color: var(--secondary-color);
}

.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 内容网格 */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    margin-top: 20px;
}

/* 内容卡片 */
.content-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    cursor: pointer;
}

.content-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

/* 卡片图片 */
.card-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    background: var(--background-color);
}

/* 卡片内容 */
.card-content {
    padding: 16px;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.card-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0 0 12px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 卡片标签 */
.card-tags {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    margin-bottom: 12px;
}

.card-tag {
    padding: 4px 8px;
    background: var(--secondary-color);
    color: var(--primary-color);
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
}

/* 卡片操作按钮 */
.card-action {
    width: 100%;
    padding: 10px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.card-action:hover {
    background: #5A67E8;
}

/* 加载更多按钮 */
.load-more-btn {
    display: block;
    margin: 30px auto 0;
    padding: 12px 24px;
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.load-more-btn:hover {
    background: var(--primary-color);
    color: white;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 48px;
    color: var(--border-color);
    margin-bottom: 16px;
}

.empty-state p {
    font-size: 16px;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .quick-actions-area {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .location-date-display {
        justify-content: center;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .filter-buttons {
        justify-content: center;
    }
    
    .section-title {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }
}
