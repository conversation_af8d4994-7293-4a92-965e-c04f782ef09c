// 全局定义Toast提示函数
function showToast(message, duration = 3000) {
    console.log('显示Toast提示:', message);

    const toast = document.getElementById('toast');
    if (!toast) {
        console.error('未找到toast元素');
        alert(message); // 如果找不到toast元素，使用alert作为备选
        return;
    }

    // 如果已经有toast在显示，先清除之前的定时器
    if (toast.timer) {
        clearTimeout(toast.timer);
    }

    // 先隐藏toast，触发重绘，重置动画
    toast.style.display = 'none';
    void toast.offsetWidth; // 触发重绘

    // 设置消息并显示
    toast.textContent = message;
    toast.style.display = 'block';

    // 设置定时器关闭toast
    toast.timer = setTimeout(() => {
        toast.style.display = 'none';
    }, duration);
}

// 确保window.showToast也可用
window.showToast = showToast;

document.addEventListener('DOMContentLoaded', function() {
    // 复制ID功能
    const copyIdBtn = document.querySelector('.copy-id-btn');
    if (copyIdBtn) {
        copyIdBtn.addEventListener('click', function() {
            const idToCopy = this.getAttribute('data-id');

            // 创建临时输入框
            const tempInput = document.createElement('input');
            tempInput.value = idToCopy;
            document.body.appendChild(tempInput);

            // 选择并复制文本
            tempInput.select();
            document.execCommand('copy');

            // 移除临时输入框
            document.body.removeChild(tempInput);

            // 显示复制成功提示，包含实际ID
            showToast('您已复制趣玩ID：' + idToCopy);

            // 添加点击动画
            this.style.transform = 'scale(1.2)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 200);
        });
    }

    // 会员卡片交互效果
    const memberBtn = document.querySelector('.member-btn');
    if (memberBtn) {
        memberBtn.addEventListener('click', function() {
            // 点击效果
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 100);

            // 显示会员开通提示
            showToast('会员功能即将上线，敬请期待');
        });
    }

    // 会员卡片悬停效果
    const memberCard = document.querySelector('.member-card');
    if (memberCard) {
        memberCard.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 6px 20px rgba(255, 165, 0, 0.15)';
        });

        memberCard.addEventListener('mouseleave', function() {
            this.style.boxShadow = 'none';
        });
    }

    // 会员权益项点击效果
    const benefitItems = document.querySelectorAll('.benefit-item');
    benefitItems.forEach(item => {
        item.addEventListener('click', function() {
            // 获取权益名称
            const benefitName = this.querySelector('.benefit-text').textContent;

            // 显示权益提示
            showToast(`${benefitName}即将上线，敬请期待`);

            // 添加点击动画
            const icon = this.querySelector('.benefit-icon');
            icon.style.transform = 'scale(1.2)';
            setTimeout(() => {
                icon.style.transform = 'scale(1)';
            }, 300);
        });
    });

    // 添加菜单项点击效果
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        item.addEventListener('click', function() {
            // 点击效果
            this.style.backgroundColor = '#f5f5f5';
            setTimeout(() => {
                this.style.backgroundColor = 'white';
            }, 150);
        });
    });

    // 底部导航栏点击效果
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            // 这里不需要移除其他项的active类，因为会跳转到新页面
            // 但为了视觉反馈，可以添加点击效果
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 100);
        });
    });

    // 检查URL参数，显示相关提示
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('settings_updated')) {
        showToast('设置已更新');
    }
});
